import json
import logging
from channels.generic.websocket import Websocket<PERSON>onsumer
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.contrib.auth.models import AnonymousUser

from device_manager.utils.auth import check_user_auth
from device_manager.models import Device

logger = logging.getLogger("app")


class TelemetryConsumer(WebsocketConsumer):
    """
    WebSocket consumer for telemetry data updates.
    Connects to device-specific groups and sends telemetry updates.
    """
    
    def connect(self):
        self.user = self.scope["user"]
        self.device_id = self.scope["url_route"]["kwargs"]["device_id"]
        self.telemetry_key = self.scope["url_route"]["kwargs"]["key"]
        self.device_group_name = f"telemetry_{self.device_id}_{self.telemetry_key}"

        # Accept the connection
        self.accept()

        # Only proceed if user is authenticated
        if not check_user_auth(self):
            return

        # Try to get the device and verify user has access
        try:
            self.device = Device.objects.get(id=self.device_id)
            
            # Check if user has access to this device
            if not (self.user.is_superuser or self.user.userprofile.devs.filter(id=self.device_id).exists()):
                logger.warning(f"User {self.user.username} attempted to access device {self.device_id} without permission")
                self.close(code=4002)  # Custom code for "access denied"
                return
            
            # Add to telemetry group
            async_to_sync(get_channel_layer().group_add)(
                self.device_group_name, self.channel_name
            )
            
            logger.info(f"User {self.user.username} connected to telemetry updates for device {self.device_id}, key {self.telemetry_key}")
            
        except Device.DoesNotExist:
            logger.error(f"Device with ID {self.device_id} not found")
            self.close(code=4002)  # Custom code for "object not found"
            return

    def disconnect(self, close_code):
        # Leave telemetry group
        if hasattr(self, 'device_group_name'):
            async_to_sync(get_channel_layer().group_discard)(
                self.device_group_name, self.channel_name
            )
            logger.info(f"User disconnected from telemetry updates for device {self.device_id}, key {self.telemetry_key}")

    def telemetry_update(self, event):
        """
        Handle telemetry update events
        """
        try:
            self.send(text_data=json.dumps(event["data"]))
        except Exception as e:
            logger.error(f"Error sending telemetry update for device {self.device_id}: {e}")
