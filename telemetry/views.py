import sys
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.shortcuts import redirect, render
from django.utils.decorators import method_decorator
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import View
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from datetime import timedelta
from telemetry.models import Telemetry


# Create your views here.
@method_decorator(login_required, name="dispatch")
class TelemetryView(LoginRequiredMixin, View):
    template_name = "telemetry/viewer.html"  # Replace with your template name

    def get(self, request, *args, **kwargs):
        key = kwargs["key"]
        title = kwargs["title"]
        device_id = kwargs["device_id"]

        context = {"key": key, "title": title, "device_id": device_id}

        return render(request, self.template_name, context)


@login_required
def get_entries(request, device_id, key):
    # Get date range parameters from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Default to last 7 days if no dates provided
    if not start_date or not end_date:
        end_date = timezone.now()
        start_date = end_date - timedelta(days=7)
    else:
        # Parse the date strings
        from django.utils.dateparse import parse_datetime
        start_date = parse_datetime(start_date)
        end_date = parse_datetime(end_date)

        # If parsing fails, default to last 7 days
        if not start_date or not end_date:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=7)

    # Filter queryset by date range
    queryset = Telemetry.objects.filter(
        devi=device_id,
        key=key,
        datetime__gte=start_date,
        datetime__lte=end_date
    ).order_by("datetime")

    telemetry_data = [
        {
            "key": telemetry.key,
            "value": telemetry.value,
            "datetime": telemetry.datetime,
        }
        for telemetry in queryset
    ]

    return JsonResponse(telemetry_data, safe=False)
