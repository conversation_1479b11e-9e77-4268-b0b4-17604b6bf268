from telemetry.models import Telemetry
from django.db import transaction
import logging
import threading

logger = logging.getLogger("app")

# Thread-local storage to prevent shared list issues in concurrent environments
_thread_local = threading.local()

def get_telemetry_list():
    if not hasattr(_thread_local, 'telemetry_list'):
        _thread_local.telemetry_list = []
    return _thread_local.telemetry_list

def process_sensory(entity, sensory_data):
    if "batt" in sensory_data:
        entity.batt = sensory_data["batt"] if not sensory_data["batt"] >= 100 else 100
    if "temp" in sensory_data:
        entity.temp = sensory_data["temp"]
    if "lati" in sensory_data:
        if sensory_data["lati"] != 0.0:
            entity.lati = sensory_data["lati"]
    if "long" in sensory_data:
        if sensory_data["long"] != 0.0:
            entity.long = sensory_data["long"]


def generate_telemetries(device, telemetries: list):
    """
    Generate telemetries for a device and save them immediately.
    This is the original implementation, kept for backward compatibility.
    """
    telemetry_list = get_telemetry_list()

    for tel in telemetries:
        if tel in device.attr["client"]:
            telemetry_list.append(Telemetry(
                devi=device,
                key=tel,
                value=device.attr["client"][tel],
                datetime=device.lupd,
            ))

    # Bulk create in smaller batches to avoid transaction timeouts
    if telemetry_list:
        Telemetry.objects.bulk_create(telemetry_list)
        telemetry_list.clear()


def batch_generate_telemetries(device, telemetries: list, batch_size=100):
    """
    Generate telemetries for a device and save them in batches.
    This is optimized for high-throughput scenarios.

    Args:
        device: The device to generate telemetries for
        telemetries: List of telemetry keys to generate
        batch_size: Number of telemetries to create in a single bulk operation
    """
    telemetry_objects = []

    try:
        # Create telemetry objects
        for tel in telemetries:
            if tel in device.attr["client"]:
                telemetry_objects.append(Telemetry(
                    devi=device,
                    key=tel,
                    value=device.attr["client"][tel],
                    datetime=device.lupd,
                ))

        # Process in batches
        for i in range(0, len(telemetry_objects), batch_size):
            batch = telemetry_objects[i:i+batch_size]
            Telemetry.objects.bulk_create(batch)

        logger.info(f"Created {len(telemetry_objects)} telemetry records for device {device.id}")
    except Exception as e:
        logger.error(f"Error generating telemetries for device {device.id}: {e}")

