from telemetry.models import Telemetry
from django.db import transaction
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
import logging
import threading

logger = logging.getLogger("app")

# Thread-local storage to prevent shared list issues in concurrent environments
_thread_local = threading.local()

def get_telemetry_list():
    if not hasattr(_thread_local, 'telemetry_list'):
        _thread_local.telemetry_list = []
    return _thread_local.telemetry_list

def send_telemetry_websocket_updates(telemetry_objects):
    """
    Send WebSocket updates for a list of telemetry objects.
    This is used when bulk_create is used since it doesn't trigger post_save signals.
    """
    try:
        channel_layer = get_channel_layer()

        # Group telemetry objects by device and key to avoid duplicate messages
        grouped_telemetry = {}
        for telemetry in telemetry_objects:
            key = f"{telemetry.devi.id}_{telemetry.key}"
            if key not in grouped_telemetry:
                grouped_telemetry[key] = []
            grouped_telemetry[key].append(telemetry)

        # Send updates for each group
        for group_key, telemetries in grouped_telemetry.items():
            # Get the latest telemetry for this device/key combination
            latest_telemetry = max(telemetries, key=lambda t: t.datetime)

            telemetry_group_name = f"telemetry_{latest_telemetry.devi.id}_{latest_telemetry.key}"

            telemetry_data = {
                "device_id": latest_telemetry.devi.id,
                "key": latest_telemetry.key,
                "value": latest_telemetry.value,
                "datetime": latest_telemetry.datetime.isoformat(),
            }

            async_to_sync(channel_layer.group_send)(
                telemetry_group_name,
                {
                    "type": "telemetry_update",
                    "data": telemetry_data,
                }
            )

        logger.debug(f"Sent WebSocket updates for {len(grouped_telemetry)} telemetry groups")

    except Exception as e:
        logger.error(f"Error sending telemetry WebSocket updates: {e}")


def process_sensory(entity, sensory_data):
    if "batt" in sensory_data:
        entity.batt = sensory_data["batt"] if not sensory_data["batt"] >= 100 else 100
    if "temp" in sensory_data:
        entity.temp = sensory_data["temp"]
    if "lati" in sensory_data:
        if sensory_data["lati"] != 0.0:
            entity.lati = sensory_data["lati"]
    if "long" in sensory_data:
        if sensory_data["long"] != 0.0:
            entity.long = sensory_data["long"]


def generate_telemetries(device, telemetries: list):
    """
    Generate telemetries for a device and save them immediately.
    This is the original implementation, kept for backward compatibility.
    """
    telemetry_list = get_telemetry_list()

    for tel in telemetries:
        if tel in device.attr["client"]:
            telemetry_list.append(Telemetry(
                devi=device,
                key=tel,
                value=device.attr["client"][tel],
                datetime=device.lupd,
            ))

    # Bulk create in smaller batches to avoid transaction timeouts
    if telemetry_list:
        Telemetry.objects.bulk_create(telemetry_list)
        # Send WebSocket updates for the created telemetry data
        send_telemetry_websocket_updates(telemetry_list)
        telemetry_list.clear()


def batch_generate_telemetries(device, telemetries: list, batch_size=100):
    """
    Generate telemetries for a device and save them in batches.
    This is optimized for high-throughput scenarios.

    Args:
        device: The device to generate telemetries for
        telemetries: List of telemetry keys to generate
        batch_size: Number of telemetries to create in a single bulk operation
    """
    telemetry_objects = []

    try:
        # Create telemetry objects
        for tel in telemetries:
            if tel in device.attr["client"]:
                telemetry_objects.append(Telemetry(
                    devi=device,
                    key=tel,
                    value=device.attr["client"][tel],
                    datetime=device.lupd,
                ))

        # Process in batches
        for i in range(0, len(telemetry_objects), batch_size):
            batch = telemetry_objects[i:i+batch_size]
            Telemetry.objects.bulk_create(batch)
            # Send WebSocket updates for each batch
            send_telemetry_websocket_updates(batch)

        logger.info(f"Created {len(telemetry_objects)} telemetry records for device {device.id}")
    except Exception as e:
        logger.error(f"Error generating telemetries for device {device.id}: {e}")

