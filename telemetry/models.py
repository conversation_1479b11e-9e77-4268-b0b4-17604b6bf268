from django.db import models
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.db.models.signals import post_save
from django.dispatch import receiver
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
import logging

from device_manager.models import Device

logger = logging.getLogger("app")


# Create your models here.
class Telemetry(models.Model):
    devi = models.ForeignKey(Device, on_delete=models.CASCADE)
    key = models.CharField(max_length=255)
    value = models.CharField(max_length=255)
    datetime = models.DateTimeField()

    def __str__(self):
        return f"{self.devi.name} - {self.key}: {self.value}"


@receiver(post_save, sender=Telemetry)
def send_telemetry_update(sender, instance: Telemetry, created, **kwargs):
    """
    Signal handler that sends telemetry updates to WebSocket channels when new telemetry data is created.
    """
    if created:  # Only send updates for newly created telemetry data
        try:
            channel_layer = get_channel_layer()

            # Send to telemetry-specific group
            telemetry_group_name = f"telemetry_{instance.devi.id}_{instance.key}"

            telemetry_data = {
                "device_id": instance.devi.id,
                "key": instance.key,
                "value": instance.value,
                "datetime": instance.datetime.isoformat(),
            }

            async_to_sync(channel_layer.group_send)(
                telemetry_group_name,
                {
                    "type": "telemetry_update",
                    "data": telemetry_data,
                }
            )

            logger.debug(f"Sent telemetry update for device {instance.devi.id}, key {instance.key}")

        except Exception as e:
            logger.error(f"Error sending telemetry WebSocket update: {e}")
