from django.test import TestCase, Client, tag
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from unittest.mock import patch, MagicMock
import json

from device_manager.models import Device
from fields.models import Field
from accounts.models import UserProfile
from telemetry.models import Telemetry
from telemetry.views import get_entries


@tag('unit')
class TelemetryViewsTestCase(TestCase):
    """Unit tests for telemetry views"""

    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            email='<EMAIL>'
        )

        # Create user profile
        self.user_profile = UserProfile.objects.create(user=self.user)

        # Create test field
        self.field = Field.objects.create(
            name="Test Field",
            cord=[{"lat": 23.5, "lng": 58.5}],
            colr="#FF0000",
            covr=10.5
        )

        # Create test device
        self.device = Device.objects.create(
            name="Test Device",
            euid="1234567890ABCDEF",
            type="Whiskers Node V1",
            fild=self.field,
            lati=23.5,
            long=58.5,
            stat="Online",
            lupd=timezone.now(),
            offp=30
        )

        # Add device to user profile
        self.user_profile.devs.add(self.device)

        # Create test telemetry data
        self.now = timezone.now()
        self.telemetry1 = Telemetry.objects.create(
            devi=self.device,
            key="Air Temperature",
            value="25.5",
            datetime=self.now - timedelta(hours=2)
        )
        self.telemetry2 = Telemetry.objects.create(
            devi=self.device,
            key="Air Temperature",
            value="26.0",
            datetime=self.now - timedelta(hours=1)
        )

        self.client = Client()

    def test_telemetry_view_get(self):
        """Test TelemetryView GET request"""
        self.client.login(username='testuser', password='testpass123')

        url = reverse('telemetry:viewer', kwargs={
            'device_id': str(self.device.id),
            'key': 'Air Temperature',
            'title': 'Temperature'
        })

        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Temperature')
        self.assertContains(response, str(self.device.id))

    def test_get_entries_without_date_filter(self):
        """Test get_entries view without date filter (should default to last 7 days)"""
        self.client.login(username='testuser', password='testpass123')

        url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'Air Temperature'
        })

        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)

        # Should return both telemetry entries as they're within last 7 days
        self.assertEqual(len(data), 2)
        self.assertEqual(data[0]['key'], 'Air Temperature')
        self.assertEqual(data[0]['value'], '25.5')

    def test_get_entries_with_date_filter(self):
        """Test get_entries view with date filter"""
        self.client.login(username='testuser', password='testpass123')

        # Filter to only get data from last hour
        start_date = (self.now - timedelta(minutes=90)).isoformat()
        end_date = self.now.isoformat()

        url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'Air Temperature'
        })

        response = self.client.get(url, {
            'start_date': start_date,
            'end_date': end_date
        })

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)

        # Should return only the second telemetry entry
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['value'], '26.0')

    def test_get_entries_unauthorized(self):
        """Test get_entries view without authentication"""
        url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'Air Temperature'
        })

        response = self.client.get(url)

        # Should redirect to login
        self.assertEqual(response.status_code, 302)


@tag('unit')
class TelemetryModelTestCase(TestCase):
    """Unit tests for telemetry model"""

    def setUp(self):
        # Create test field
        self.field = Field.objects.create(
            name="Test Field",
            cord=[{"lat": 23.5, "lng": 58.5}],
            colr="#FF0000",
            covr=10.5
        )

        # Create test device
        self.device = Device.objects.create(
            name="Test Device",
            euid="1234567890ABCDEF",
            type="Whiskers Node V1",
            fild=self.field,
            lati=23.5,
            long=58.5,
            stat="Online",
            lupd=timezone.now(),
            offp=30
        )

    @patch('telemetry.models.async_to_sync')
    @patch('telemetry.models.get_channel_layer')
    def test_telemetry_post_save_signal(self, mock_get_channel_layer, mock_async_to_sync):
        """Test that post_save signal sends WebSocket update"""
        # Create telemetry data
        telemetry = Telemetry.objects.create(
            devi=self.device,
            key="Air Temperature",
            value="25.5",
            datetime=timezone.now()
        )

        # Verify WebSocket update was sent
        mock_get_channel_layer.assert_called_once()
        mock_async_to_sync.assert_called_once()

    def test_telemetry_str_method(self):
        """Test telemetry string representation"""
        telemetry = Telemetry.objects.create(
            devi=self.device,
            key="Air Temperature",
            value="25.5",
            datetime=timezone.now()
        )

        expected_str = f"{self.device.name} - Air Temperature: 25.5"
        self.assertEqual(str(telemetry), expected_str)
