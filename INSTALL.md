# WhiskersHub Installation Guide

This guide will help you install and set up WhiskersHub on your system.

## Prerequisites

Before installing WhiskersHub, you need a Linux-based system (Ubuntu recommended). The installation script will automatically install the following dependencies:

- Docker (version 28.1.1)
- GNU Make (version 4.3)

## Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/your-organization/WhiskersHub.git
cd WhiskersHub
```

### 2. Using the Dependencies Installation Script

The easiest way to set up the required dependencies is to use our installation script:

```bash
# Make the script executable
chmod +x install_dependencies.sh

# Run the installation script
./install_dependencies.sh
```

This script will:
- Install Docker 28.1.1
- Install GNU Make 4.3
- Create a `.env` file from `.env.example` if it doesn't exist
- Create necessary directories (staticfiles, media, logs)
- Set appropriate permissions

If you encounter a "Permission denied" error when running the script, make sure to set the executable permission:

```bash
chmod +x install_dependencies.sh
```

## Starting the Application

### Production Mode

To start WhiskersHub in production mode:

```bash
make up
```

## Accessing the Application

Once the application is running, you can access it at:

- Web Interface: http://localhost
- Admin Interface: http://localhost/admin

## Available Commands

Run `make help` to see all available commands:

```
Available commands:
  make up              - Start the application in production mode
  make down            - Stop all containers
  make migrations      - Create and apply database migrations
  make collectstatic   - Collect static files
  make help            - Show this help message
```

## Troubleshooting

### Installation Script Issues

#### Permission Denied

If you see `Permission denied` when running the installation script:

```bash
chmod +x install_dependencies.sh
```

#### Failed to Install Docker Version 28.1.1

If the script fails to install Docker version 28.1.1:

1. The script will automatically try alternative version formats and fall back to the latest version
2. You can manually install Docker using the official Docker documentation
3. Check if your system is compatible with Docker 28.1.1

#### Failed to Install GNU Make 4.3

If the script fails to install GNU Make 4.3:

```bash
# Install build dependencies
sudo apt-get update
sudo apt-get install -y build-essential wget

# Download and install GNU Make 4.3 manually
cd /tmp
wget https://ftp.gnu.org/gnu/make/make-4.3.tar.gz
tar -xzf make-4.3.tar.gz
cd make-4.3
./configure
make
sudo make install
```

### Database Connection Issues

If you encounter database connection issues:

```bash
# Restart all containers
make down
make up

# Check if pgbouncer is running correctly
docker compose ps pgbouncer
docker compose logs pgbouncer

# Check database connection directly
docker compose exec db psql -U postgres -c "SELECT 1;"
```

### Permission Issues

#### Static Files and Media Directories

If you encounter permission issues with directories:

```bash
# Set proper permissions
sudo chmod -R 755 staticfiles media logs

# If using Docker, fix ownership inside containers
docker compose exec web bash -c "mkdir -p /home/<USER>/staticfiles && chmod -R 755 /home/<USER>/staticfiles"
docker compose exec web bash -c "mkdir -p /home/<USER>/media && chmod -R 755 /home/<USER>/media"
```

#### Docker Permission Issues

If you encounter permission issues with Docker:

```bash
# Add your user to the docker group
sudo usermod -aG docker $USER

# Apply the changes (requires logout/login)
newgrp docker

# Verify docker permissions
docker info
```

### Docker Issues

#### Container Startup Failures

If containers fail to start:

```bash
# View container logs
docker compose logs

# View logs for a specific service
docker compose logs web
docker compose logs db
docker compose logs pgbouncer

# Clean up Docker resources
docker system prune -a
make up
```

#### Collectstatic Failures

If the `collectstatic` command fails:

```bash
# Check for permission issues
sudo chmod -R 755 staticfiles

# Run collectstatic manually
docker compose exec web python manage.py collectstatic --noinput --clear

# Check for errors in the Django settings
docker compose exec web python manage.py check
```

#### Health Check Failures

If health checks are failing:

```bash
# Check service status
docker compose ps

# View health check logs
docker inspect --format "{{json .State.Health }}" $(docker compose ps -q pgbouncer) | jq

# Restart the problematic service
docker compose restart pgbouncer
```

## Support

If you need help with installation or encounter any issues, please contact the WhiskersHub support team.

## Updating WhiskersHub

To update WhiskersHub to the latest version:

```bash
# Pull the latest changes
git pull

# Rebuild and restart containers
make restart
```
