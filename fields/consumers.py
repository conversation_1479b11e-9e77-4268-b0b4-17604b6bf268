import json
from accounts.models import UserProfile
from auth.scripts import check_user_auth
from channels.generic.websocket import WebsocketConsumer
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.forms.models import model_to_dict
import logging
from fields.models import Field

logger = logging.getLogger("app")


class FieldConsumer(WebsocketConsumer):
    def connect(self):
        self.user = self.scope["user"]

        self.accept()

        # Only proceed if user is authenticated
        if not check_user_auth(self):
            return

    def disconnect(self, close_code):
        # Leave field group if it exists
        if hasattr(self, "fields"):
            fields = getattr(self, "fields", [])
            for field in fields:
                async_to_sync(get_channel_layer().group_discard)(
                    f"field_{field}", self.channel_name
                )

    def receive(self, text_data):
        try:
            # Load the JSON data
            data = json.loads(text_data)

            # Check if data is a list
            if isinstance(data, list):
                self.fields = data
            else:
                raise ValueError("Expected a list of field IDs")
        except (json.JSONDecodeError, ValueError):
            logger.error("Invalid JSON data received")
            self.close(4003)
            return

        self.handle_subscription()
        self.send_initial_data()

    def handle_subscription(self):
        for field in self.fields:
            async_to_sync(get_channel_layer().group_add)(
                f"field_{field}", self.channel_name
            )

    def send_initial_data(self):
        fields_list = Field.objects.filter(id__in=self.fields)
        fields_dict = [field.to_dict() for field in fields_list]

        self.send(json.dumps(fields_dict))

    def object_update(self, event):
        self.send(text_data=json.dumps([event["data"]]))
