import logging
import time
import traceback
from functools import wraps
from django.db import connection
from django.http import HttpResponse, JsonResponse

logger = logging.getLogger("app")

def safe_db_operation(func):
    """
    Decorator to safely handle database operations with proper error handling.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Database operation error in {func.__name__}: {e}")
            logger.error(traceback.format_exc())
            return None
    return wrapper

class RequestLoggingMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        start_time = time.time()

        try:
            response = self.get_response(request)
            duration = time.time() - start_time

            logger.info(
                f"Method: {request.method}, Path: {request.path}, "
                f"Status: {response.status_code}, Time: {duration:.4f}s"
            )
            return response
        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                f"Method: {request.method}, Path: {request.path}, "
                f"Error: {str(e)}, Time: {duration:.4f}s"
            )
            logger.error(traceback.format_exc())

            # Return a graceful error response
            if request.path.startswith('/routes/ttn-app/'):
                # For TTN endpoint, return 202 Accepted to prevent retries
                return HttpResponse(status=202, content="Accepted with errors")
            elif request.headers.get('Accept') == 'application/json' or request.path.endswith('.json'):
                # For API requests, return JSON error
                return JsonResponse({'error': 'Internal server error'}, status=500)
            else:
                # For regular requests, return simple error
                return HttpResponse(status=500, content="Internal server error")


class TimeoutMiddleware:
    """
    Middleware to set a timeout for database operations on a per-request basis
    and handle connection issues gracefully.
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Set appropriate timeout based on endpoint
        if request.path.startswith('/routes/ttn-app/'):
            # Very short timeout for TTN app endpoint
            timeout_ms = 5000  # 5 seconds
        elif any(request.path.startswith(p) for p in ['/device/list/', '/telemetry/', '/notification_center/']):
            # Medium timeout for data-heavy endpoints
            timeout_ms = 15000  # 15 seconds
        else:
            # Default timeout for other endpoints
            timeout_ms = 30000  # 30 seconds

        # Set the timeout
        self._set_statement_timeout(timeout_ms)

        try:
            # Process the request
            response = self.get_response(request)
            return response
        except Exception as e:
            logger.error(f"Request error in TimeoutMiddleware: {e}")
            logger.error(traceback.format_exc())

            # Return a graceful error response
            if request.path.startswith('/routes/ttn-app/'):
                return HttpResponse(status=202, content="Accepted with errors")
            elif request.headers.get('Accept') == 'application/json' or request.path.endswith('.json'):
                return JsonResponse({'error': 'Internal server error'}, status=500)
            else:
                return HttpResponse(status=500, content="Internal server error")
        finally:
            # Always reset the timeout, even if an exception occurred
            self._reset_statement_timeout()

    @safe_db_operation
    def _set_statement_timeout(self, timeout_ms):
        """Set statement timeout in milliseconds with proper error handling"""
        with connection.cursor() as cursor:
            cursor.execute(f"SET statement_timeout = {timeout_ms}")

    @safe_db_operation
    def _reset_statement_timeout(self):
        """Reset statement timeout to default with proper error handling"""
        with connection.cursor() as cursor:
            cursor.execute("SET statement_timeout = DEFAULT")


class ConnectionHealthMiddleware:
    """
    Middleware to check database connection health before processing requests.
    This helps prevent RemoteDisconnected errors by ensuring the connection is valid.
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check connection health before processing request
        if not self._check_connection_health():
            self._reset_connection()

        try:
            # Process the request
            response = self.get_response(request)
            return response
        except Exception as e:
            logger.error(f"Request error in ConnectionHealthMiddleware: {e}")
            logger.error(traceback.format_exc())

            # Try to reset the connection
            self._reset_connection()

            # Return a graceful error response
            if request.path.startswith('/routes/ttn-app/'):
                return HttpResponse(status=202, content="Accepted with errors")
            elif request.headers.get('Accept') == 'application/json' or request.path.endswith('.json'):
                return JsonResponse({'error': 'Internal server error'}, status=500)
            else:
                return HttpResponse(status=500, content="Internal server error")

    @safe_db_operation
    def _check_connection_health(self):
        """Check if the database connection is healthy"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                return cursor.fetchone()[0] == 1
        except Exception:
            return False

    @safe_db_operation
    def _reset_connection(self):
        """Reset the database connection"""
        try:
            connection.close()
            connection.connect()
            logger.info("Database connection reset successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to reset database connection: {e}")
            return False
