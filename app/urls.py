"""app URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import include, path
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path("", include("network_monitor.urls")),
    path("accounts/", include("accounts.urls")),
    path("admin/", admin.site.urls),
    path("device/", include("device_manager.urls")),
    path("packet_analyzer/", include("packet_analyzer.urls")),
    path("fields/", include("fields.urls")),
    path("notification_center/", include("notification_center.urls")),
    path("settings/", include("settings.urls")),
    path("telemetry/", include("telemetry.urls")),
    path("configure/", include("configuration.urls")),
    path("routes/", include("routes.urls")),
    path("webpush/", include("webpush.urls")),
    path("health/", include("health.urls")),
    path("", include("pwa.urls")),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
