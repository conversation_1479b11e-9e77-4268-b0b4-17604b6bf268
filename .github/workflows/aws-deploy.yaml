---
name: Deploy to AWS

on:
  # Trigger deployment on push to dev branch
  push:
    branches:
      - dev
  # Allow manual triggering
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

# Cancel any previously-started, yet still active runs of this workflow on the same branch
concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  # Deploy to AWS ECS
  deploy-to-aws:
    name: Deploy to AWS
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'staging' }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        run: |
          echo "${{ secrets.JUMP_SERVER_SSH_KEY }}" > jump.pem
          echo "${{ secrets.APP_SERVER_SSH_KEY }}" > app.pem
          chmod 400 jump.pem app.pem
          ssh -o StrictHostKeyChecking=no -i jump.pem ubuntu@${{ vars.JUMP_IP }} "rm -f /tmp/app.pem"
          scp -o StrictHostKeyChecking=no -i jump.pem app.pem ubuntu@${{ vars.JUMP_IP }}:/tmp/app.pem
          ssh -o StrictHostKeyChecking=no -i jump.pem ubuntu@${{ vars.JUMP_IP }} << 'JUMP_EOF'
            ssh -o StrictHostKeyChecking=no -i /tmp/app.pem ubuntu@${{ vars.APP_IP }}  << 'INNER_EOF'
            echo "Connected to Application Server."

            cd /home/<USER>/WhiskersHub

            echo "Stashing any local changes (if any)..."
            git stash || true

            echo "Pulling latest code from GitHub..."
            git pull origin dev

            echo "Building Docker images and Starting Docker containers..."
            make restart

            echo "Deployment Completed Successfully!"
          INNER_EOF
            rm -rf /tmp/app.pem
            exit 0
          JUMP_EOF
