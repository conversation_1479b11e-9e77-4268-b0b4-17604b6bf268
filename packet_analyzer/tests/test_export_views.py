from django.test import TestCase, Client, tag
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth.models import User
from datetime import timedelta
import json
import csv
from io import StringIO

from packet_analyzer.models import UplinkPacket, DownlinkPacket, DroppedPacket
from device_manager.models import Device
from fields.models import Field
from accounts.models import UserProfile


class ExportViewTestBase(TestCase):
    """Base class for export view tests"""

    def setUp(self):
        # Create test users
        self.regular_user = User.objects.create_user(
            username='testuser',
            password='testpassword',
            email='<EMAIL>'
        )

        self.superuser = User.objects.create_superuser(
            username='admin',
            password='adminpassword',
            email='<EMAIL>'
        )

        # Create test field
        self.field = Field.objects.create(
            name="Test Field",
            cord=json.dumps([{"lat": 23.5, "lng": 58.4}]),
            colr="#FF5733",
            covr=10.5,
            loca="Test Location",
            work_shifts={}
        )

        # Create test devices
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Device Description",
            euid="ABCDEF0123456789",
            type="Whiskers Node V1",
            fild=self.field,
            offp=1
        )

        self.gateway = Device.objects.create(
            name="Test Gateway",
            desc="Test Gateway Description",
            euid="9876543210ABCDEF",
            type="Whiskers Gateway V1",
            fild=self.field,
            offp=2
        )

        # Create user profiles and assign devices
        self.user_profile = UserProfile.objects.create(
            user=self.regular_user,
            role="User",
            titl="Test Title",
            orgn="Test Organization"
        )
        self.user_profile.devs.add(self.device)

        self.admin_profile = UserProfile.objects.create(
            user=self.superuser,
            role="Admin",
            titl="Admin Title",
            orgn="Admin Organization"
        )
        self.admin_profile.devs.add(self.device, self.gateway)

        # Create test client
        self.client = Client()

        # Create packets
        self.now = timezone.now()

        # Create uplink packets
        for i in range(5):
            UplinkPacket.objects.create(
                devi=self.device,
                gate=self.gateway,
                data=f"DATA{i}",
                deco={"test": f"data{i}"},
                cont=i,
                rssi=-70 + i,
                snr=5.5 + (i * 0.1),
                chan=1,
                freq=868,
                txat=self.now - timedelta(minutes=30 - i),
                rxat=self.now - timedelta(minutes=25 - i)
            )

        # Create downlink packets
        for i in range(5):
            DownlinkPacket.objects.create(
                devi=self.device,
                gate=self.gateway,
                data=f"DATA{i}",
                deco={"test": f"data{i}"},
                cont=i,
                chan=1,
                freq=868,
                txpr=14.0 + (i * 0.1),
                crat=self.now - timedelta(minutes=30 - i),
                txat=self.now - timedelta(minutes=25 - i)
            )

        # Create dropped packets
        for i in range(5):
            DroppedPacket.objects.create(
                devi=self.device if i % 2 == 0 else None,
                gate=self.gateway if i % 3 == 0 else None,
                data={"error": f"test_error_{i}"},
                expt=f"Test exception {i}",
                rxat=self.now - timedelta(minutes=30 - i)
            )


@tag('integration')
class ExportToCSVIntegrationTest(ExportViewTestBase):
    """Integration tests for export_to_csv view"""

    def test_export_to_csv_requires_login(self):
        """Test that the export_to_csv view requires login"""
        response = self.client.get(reverse('packet_analyzer:export_csv'))
        self.assertEqual(response.status_code, 302)  # Redirects to login page
        self.assertIn('login', response.url)

    def test_export_to_csv_with_incorrect_credentials(self):
        """Test that the export_to_csv view rejects incorrect credentials"""
        # Try to login with incorrect password
        login_success = self.client.login(username='testuser', password='wrongpassword')
        self.assertFalse(login_success)

        # Try to access the view after failed login
        response = self.client.get(reverse('packet_analyzer:export_csv'))
        self.assertEqual(response.status_code, 302)  # Still redirects to login page
        self.assertIn('login', response.url)

    def test_export_to_csv_without_packet_status(self):
        """Test that the export_to_csv view handles missing packet_status parameter"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:export_csv'))

        # The view should still return a response, even if it's empty
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="notification_export.csv"')

    def test_export_uplink_to_csv(self):
        """Test exporting uplink packets to CSV"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:export_csv') + '?packet_status=uplink')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="uplink_export.csv"')

        # Parse the CSV content
        content = response.content.decode('utf-8')
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        # Check the header row
        self.assertEqual(rows[0], [
            'Device', 'Gateway', 'Packet Count', 'RSSI', 'SNR',
            'Channel', 'Frequency', 'Transmitted At', 'Received At',
            'Data', 'Decoded Packet'
        ])

        # Check that we have the expected number of data rows
        # We should have 5 uplink packets for the user's device
        self.assertEqual(len(rows) - 1, 5)

    def test_export_downlink_to_csv(self):
        """Test exporting downlink packets to CSV"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:export_csv') + '?packet_status=downlink')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="downlink_export.csv"')

        # Parse the CSV content
        content = response.content.decode('utf-8')
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        # Check the header row
        self.assertEqual(rows[0], [
            'Gateway', 'Device', 'Packet Count', 'Decoded Packet', 'Data',
            'Channel', 'Frequency', 'Transmit Power', 'Created At',
            'Transmitted At'
        ])

        # Check that we have the expected number of data rows
        # We should have 5 downlink packets for the user's device
        self.assertEqual(len(rows) - 1, 5)

    def test_export_dropped_to_csv_requires_superuser(self):
        """Test that exporting dropped packets to CSV requires superuser"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:export_csv') + '?packet_status=dropped')

        self.assertEqual(response.status_code, 403)  # Forbidden

    def test_export_dropped_to_csv_with_superuser(self):
        """Test exporting dropped packets to CSV with superuser"""
        self.client.login(username='admin', password='adminpassword')
        response = self.client.get(reverse('packet_analyzer:export_csv') + '?packet_status=dropped')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="dropped_export.csv"')

        # Parse the CSV content
        content = response.content.decode('utf-8')
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        # Check the header row
        self.assertEqual(rows[0], [
            'Device', 'Gateway', 'Data', 'Received At', 'Exception'
        ])

        # Check that we have the expected number of data rows
        # We should have 5 dropped packets
        self.assertEqual(len(rows) - 1, 5)

    def test_export_to_csv_with_invalid_packet_status(self):
        """Test that the export_to_csv view handles invalid packet status"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:export_csv') + '?packet_status=invalid')

        self.assertEqual(response.status_code, 400)  # Bad Request


@tag('e2e')
class ExportToCSVE2ETest(ExportViewTestBase):
    """End-to-end tests for export_to_csv view"""

    def test_export_uplink_to_csv_with_real_login(self):
        """Test exporting uplink packets to CSV with real login"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:export_csv') + '?packet_status=uplink')

        self.assertEqual(response.status_code, 200)

        # Parse the CSV content
        content = response.content.decode('utf-8')
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        # Check that the data rows contain the expected device name
        for i in range(1, len(rows)):
            self.assertEqual(rows[i][0], 'Test Device')

    def test_export_downlink_to_csv_with_real_login(self):
        """Test exporting downlink packets to CSV with real login"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:export_csv') + '?packet_status=downlink')

        self.assertEqual(response.status_code, 200)

        # Parse the CSV content
        content = response.content.decode('utf-8')
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        # Check that the data rows contain the expected device name
        for i in range(1, len(rows)):
            self.assertEqual(rows[i][1], 'Test Device')

    def test_export_dropped_to_csv_with_real_superuser(self):
        """Test exporting dropped packets to CSV with real superuser"""
        self.client.login(username='admin', password='adminpassword')
        response = self.client.get(reverse('packet_analyzer:export_csv') + '?packet_status=dropped')

        self.assertEqual(response.status_code, 200)

        # Parse the CSV content
        content = response.content.decode('utf-8')
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        # Check that we have the expected number of data rows
        self.assertEqual(len(rows) - 1, 5)

    def test_export_to_csv_with_empty_database(self):
        """Test that the export_to_csv view handles an empty database gracefully"""
        # Delete all packets
        UplinkPacket.objects.all().delete()
        DownlinkPacket.objects.all().delete()
        DroppedPacket.objects.all().delete()

        # Test uplink export with empty database
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:export_csv') + '?packet_status=uplink')
        self.assertEqual(response.status_code, 200)

        # Parse the CSV content
        content = response.content.decode('utf-8')
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        # Should have only the header row
        self.assertEqual(len(rows), 1)

    def test_export_to_csv_with_user_without_devices(self):
        """Test that the export_to_csv view handles users without devices gracefully"""
        # Create a new user without access to any devices
        new_user = User.objects.create_user(
            username='newuser',
            password='newpassword',
            email='<EMAIL>'
        )

        UserProfile.objects.create(
            user=new_user,
            role="User",
            titl="New User",
            orgn="Test Organization"
        )

        # Login as the new user
        self.client.login(username='newuser', password='newpassword')
        response = self.client.get(reverse('packet_analyzer:export_csv') + '?packet_status=uplink')
        self.assertEqual(response.status_code, 200)

        # Parse the CSV content
        content = response.content.decode('utf-8')
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        # Should have only the header row
        self.assertEqual(len(rows), 1)

    def test_export_to_csv_with_different_http_methods(self):
        """Test that the export_to_csv view works with different HTTP methods"""
        self.client.login(username='testuser', password='testpassword')

        # Test with POST method
        response = self.client.post(reverse('packet_analyzer:export_csv') + '?packet_status=uplink')
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')

        # Test with GET method
        response = self.client.get(reverse('packet_analyzer:export_csv') + '?packet_status=uplink')
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')

    def test_export_to_csv_with_malformed_url(self):
        """Test that the export_to_csv view handles malformed URLs gracefully"""
        self.client.login(username='testuser', password='testpassword')

        # Test with malformed query parameters
        response = self.client.get(reverse('packet_analyzer:export_csv') + '?packet_status=uplink&invalid=param')
        self.assertEqual(response.status_code, 200)  # Should ignore invalid parameters
