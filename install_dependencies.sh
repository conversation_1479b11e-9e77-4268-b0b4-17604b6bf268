#!/bin/bash
set -e

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting WhiskersHub installation...${NC}"

# Check if Docker is installed
if command -v docker &> /dev/null; then
  for pkg in docker.io docker-doc docker-compose docker-compose-v2 podman-docker containerd runc; do sudo apt-get remove $pkg; done
fi

# Check if Docker Compose is installed
if command -v docker compose &> /dev/null; then
    for pkg in docker.io docker-doc docker-compose docker-compose-v2 podman-docker containerd runc; do sudo apt-get remove $pkg; done
fi

# Add Docker's official GPG key:
sudo apt-get update
sudo apt-get install ca-certificates curl -y
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc

# Add the repository to Apt sources:
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
  $(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update

#installing specific docker version 28.1.1
echo -e "${YELLOW}Installing Docker version 28.1.1...${NC}"

# List available versions
echo -e "${YELLOW}Available Docker versions:${NC}"
apt-cache madison docker-ce | head -n 10

# Install specific Docker version 28.1.1
if sudo apt-get install docker-ce=5:28.1.1-1~ubuntu.$(lsb_release -rs)~$(lsb_release -cs) \
                     docker-ce-cli=5:28.1.1-1~ubuntu.$(lsb_release -rs)~$(lsb_release -cs) \
                     containerd.io \
                     docker-buildx-plugin \
                     docker-compose-plugin -y; then
    echo -e "${GREEN}Successfully installed Docker version 28.1.1${NC}"

    # Prevent automatic updates
    sudo apt-mark hold docker-ce docker-ce-cli
else

    echo -e "${RED}Could not install Docker version 28.1.1-1. Falling back to latest version...${NC}"

    # Fall back to latest version
    sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin -y
    return 0
fi

sudo systemctl start docker
sudo systemctl enable docker  # Optional: start on boot

# Verify Docker installation
echo -e "${YELLOW}Verifying Docker installation...${NC}"
docker_version=$(docker --version)
echo -e "${GREEN}Installed Docker version: ${docker_version}${NC}"

# Check if the correct version is installed
if [[ $docker_version == *"28.1.1"* ]]; then
  echo -e "${GREEN}Successfully installed Docker version 28.1.1!${NC}"
else
  echo -e "${RED}Warning: Docker version 28.1.1 may not have been installed correctly.${NC}"
  echo -e "${RED}Installed version: ${docker_version}${NC}"
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo -e "${YELLOW}Creating .env file from .env.example${NC}"
    cp .env.example .env
    echo -e "${GREEN}Created .env file. Please update it with your configuration.${NC}"
else
    echo -e "${YELLOW}.env file already exists. Skipping...${NC}"
fi

# Install GNU Make 4.3
echo -e "${YELLOW}Installing GNU Make 4.3...${NC}"

# Check if make is already installed
if command -v make &> /dev/null; then
    current_make_version=$(make --version | head -n 1 | awk '{print $3}')
    echo -e "${YELLOW}Current GNU Make version: ${current_make_version}${NC}"

    # If it's already version 4.3, skip installation
    if [[ "$current_make_version" == "4.3" ]]; then
        echo -e "${GREEN}GNU Make 4.3 is already installed.${NC}"
    else
        echo -e "${YELLOW}Upgrading GNU Make to version 4.3...${NC}"

        # Install build dependencies
        sudo apt-get update
        sudo apt-get install -y build-essential wget

        # Download and extract GNU Make 4.3
        cd /tmp
        wget https://ftp.gnu.org/gnu/make/make-4.3.tar.gz
        tar -xzf make-4.3.tar.gz
        cd make-4.3

        # Configure, build and install
        ./configure
        make
        sudo make install

        # Verify installation
        new_make_version=$(/usr/local/bin/make --version | head -n 1 | awk '{print $3}')
        echo -e "${GREEN}Installed GNU Make version: ${new_make_version}${NC}"

        # Create symlink if needed
        sudo ln -sf /usr/local/bin/make /usr/bin/make

        # Return to original directory
        cd - > /dev/null
    fi
else
    echo -e "${YELLOW}GNU Make not found. Installing version 4.3...${NC}"

    # Install build dependencies
    sudo apt-get update
    sudo apt-get install -y build-essential wget

    # Download and extract GNU Make 4.3
    cd /tmp
    wget https://ftp.gnu.org/gnu/make/make-4.3.tar.gz
    tar -xzf make-4.3.tar.gz
    cd make-4.3

    # Configure, build and install
    ./configure
    make
    sudo make install

    # Verify installation
    make_version=$(/usr/local/bin/make --version | head -n 1 | awk '{print $3}')
    echo -e "${GREEN}Installed GNU Make version: ${make_version}${NC}"

    # Create symlink
    sudo ln -sf /usr/local/bin/make /usr/bin/make

    # Return to original directory
    cd - > /dev/null
fi

echo -e "${GREEN}Installation completed successfully!${NC}"
echo -e "${GREEN}You can now start the application with:${NC}"
echo -e "${YELLOW}make up${NC}"
echo -e "${GREEN}Access the application at:${NC}"
echo -e "${YELLOW}http://localhost${NC}"

