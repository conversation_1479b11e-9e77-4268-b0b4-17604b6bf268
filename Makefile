up:
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
	fi
	@echo "Starting WhiskersHub in Production Mode! Network server will be started."
	docker compose up --build --force-recreate db pgbouncer web redis nginx --detach

up-dev:
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
	fi
	@echo "Starting WhiskersHub in Development Mode! Logs printed to console..."
	docker compose up --build --force-recreate db pgbouncer web redis nginx

down:
	docker compose down --remove-orphans

restart:
	@echo "Restarting WhiskersHub..."
	@$(MAKE) down
	@$(MAKE) up
# ignore entrypoint.sh
migrations:
	docker compose run web python manage.py makemigrations
	docker compose run web python manage.py migrate

collectstatic:
	docker compose run web python manage.py collectstatic --noinput

# Help command
help:
	@echo "Available commands:"
	@echo "  make up              - Start the application in production mode"
	@echo "  make down            - Stop all containers"
	@echo "  make restart         - Restart the application (down then up)"
	@echo "  make migrations      - Create and apply database migrations"
	@echo "  make collectstatic   - Collect static files"
	@echo "  make help            - Show this help message"

createdemo:
	docker compose run web python scripts/create_demo_devices.py

updatedemo:
	docker compose run web python scripts/update_demo_devices.py