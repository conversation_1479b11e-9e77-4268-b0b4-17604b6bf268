from django.conf import settings
from device_manager.models import Device
from django_redis import get_redis_connection

# Get Redis connection from django-redis cache
redis_client = get_redis_connection("default")

FIELD_CACHE_KEY = "field:{field_id}"
FIELD_CACHE_TIMEOUT = 3600  # 1 hour

def get_device(euid):
    try:
        # Check Redis cache for the device
        device_data = redis_client.get(f"device:{euid}")
        if device_data:
            device = Device.from_json(device_data)
            return device
    except Exception:
        # Redis error, fall back to database
        pass

    # Fallback to database query if not in cache or Redis error
    device = Device.objects.filter(euid=euid).first()
    if device:
        try:
            # Cache the device data in Redis
            redis_client.set(f"device:{euid}", device.to_json(), ex=3600)  # Cache for 1 hour
        except Exception:
            # Redis error, continue without caching
            pass
    return device
    

def get_cached_field(field_id):
    """
    Get a field from Redis cache

    Args:
        field_id: The field ID

    Returns:
        Field object or None if not in cache
    """
    from fields.models import Field

    field_data = redis_client.get(FIELD_CACHE_KEY.format(field_id=field_id))
    if field_data:
        try:
            return Field.from_json(field_data)
        except Exception:
            return None
    return None