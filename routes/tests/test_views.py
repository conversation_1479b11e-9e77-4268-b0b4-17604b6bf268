"""
Unit, integration and e2e tests for routes.views module
"""
import json
from unittest.mock import patch, MagicMock, call
from django.test import TestCase, RequestFactory, tag
from django.http import HttpResponse
from django.conf import settings
from django.utils import timezone
from datetime import datetime

from routes.views import ttn_app, _process_drop_packet, _process_uplink
from routes.tests.test_utils import BaseRoutesTestCase
from device_manager.models import Device
from packet_analyzer.models import DroppedPacket, UplinkPacket


@tag('unit')
class TTNAppViewUnitTestCase(BaseRoutesTestCase):
    """Unit tests for ttn_app view function"""

    def setUp(self):
        """Set up test data"""
        super().setUp()
        self.factory = RequestFactory()
        
        # Sample TTN payload
        self.valid_payload = {
            "end_device_ids": {
                "dev_eui": self.device.euid
            },
            "uplink_message": {
                "f_cnt": 123,
                "rx_metadata": [{
                    "gateway_ids": {"eui": self.gateway.euid},
                    "rssi": -80,
                    "snr": 10.5,
                    "time": "2023-01-01T12:00:00Z",
                    "received_at": "2023-01-01T12:00:01Z",
                    "channel_index": 1
                }],
                "settings": {
                    "frequency": 868100000.0
                },
                "decoded_payload": {
                    "messages": [[
                        {
                            "type": "Air Temperature",
                            "measurementValue": 25.5
                        },
                        {
                            "type": "Battery",
                            "measurementValue": 85
                        }
                    ]],
                    "payload": "base64encodeddata"
                }
            }
        }

    def test_ttn_app_get_method_not_allowed(self):
        """Test that GET method returns method not allowed"""
        request = self.factory.get('/routes/ttn-app/')

        # Since the view doesn't explicitly handle GET, it should process it
        # but return an error due to missing POST data
        with patch('routes.views._process_drop_packet') as mock_drop:
            mock_drop.return_value = MagicMock(id=1, expt="Test")
            try:
                response = ttn_app(request)
                # If we get a response, it should be 400
                if response:
                    self.assertEqual(response.status_code, 400)
                else:
                    # If response is None, the view didn't handle the request properly
                    self.fail("View returned None instead of HttpResponse")
            except Exception:
                # If an exception is raised, that's also acceptable for invalid requests
                pass

    def test_ttn_app_invalid_json(self):
        """Test TTN app with invalid JSON payload"""
        request = self.factory.post(
            '/routes/ttn-app/',
            data='invalid json',
            content_type='application/json'
        )
        
        with patch('routes.views._process_drop_packet') as mock_drop:
            response = ttn_app(request)
        
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.content.decode(), "Invalid JSON Format")
        mock_drop.assert_called_once()

    def test_ttn_app_missing_authorization_header(self):
        """Test TTN app without Authorization header"""
        request = self.factory.post(
            '/routes/ttn-app/',
            data=json.dumps(self.valid_payload),
            content_type='application/json'
        )
        
        with patch('routes.views._process_drop_packet') as mock_drop:
            response = ttn_app(request)
        
        self.assertEqual(response.status_code, 401)
        self.assertEqual(response.content.decode(), "No API key in request headers")
        mock_drop.assert_called_once()

    def test_ttn_app_invalid_api_key(self):
        """Test TTN app with invalid API key"""
        request = self.factory.post(
            '/routes/ttn-app/',
            data=json.dumps(self.valid_payload),
            content_type='application/json',
            HTTP_AUTHORIZATION='invalid_key'
        )
        
        with patch('routes.views._process_drop_packet') as mock_drop:
            response = ttn_app(request)
        
        self.assertEqual(response.status_code, 401)
        self.assertEqual(response.content.decode(), "Invalid API key in request headers")
        mock_drop.assert_called_once()

    @patch('routes.views.settings.TTN_API_KEY', 'valid_api_key')
    def test_ttn_app_missing_end_device_ids(self):
        """Test TTN app with missing end_device_ids"""
        payload = {"some_other_field": "value"}
        request = self.factory.post(
            '/routes/ttn-app/',
            data=json.dumps(payload),
            content_type='application/json',
            HTTP_AUTHORIZATION='valid_api_key'
        )
        
        with patch('routes.views._process_drop_packet') as mock_drop:
            response = ttn_app(request)
        
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.content.decode(), "Missing 'end_device_ids' in payload")
        mock_drop.assert_called_once()

    @patch('routes.views.settings.TTN_API_KEY', 'valid_api_key')
    @patch('routes.views.get_device')
    def test_ttn_app_device_not_found(self, mock_get_device):
        """Test TTN app when device is not found"""
        mock_get_device.return_value = None
        
        request = self.factory.post(
            '/routes/ttn-app/',
            data=json.dumps(self.valid_payload),
            content_type='application/json',
            HTTP_AUTHORIZATION='valid_api_key'
        )
        
        response = ttn_app(request)
        
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.content.decode(), "Device not found")
        mock_get_device.assert_called_once_with(self.device.euid)

    @patch('routes.views.settings.TTN_API_KEY', 'valid_api_key')
    @patch('routes.views.get_device')
    @patch('routes.views.ttn_executor.submit')
    def test_ttn_app_successful_uplink_processing(self, mock_submit, mock_get_device):
        """Test successful TTN app uplink processing"""
        mock_get_device.return_value = self.device
        
        request = self.factory.post(
            '/routes/ttn-app/',
            data=json.dumps(self.valid_payload),
            content_type='application/json',
            HTTP_AUTHORIZATION='valid_api_key'
        )
        
        response = ttn_app(request)
        
        self.assertIn(response.status_code, [200,202])
        mock_get_device.assert_called_once_with(self.device.euid)
        mock_submit.assert_called_once()  # Check that async task was submitted

    @patch('routes.views.settings.TTN_API_KEY', 'valid_api_key')
    @patch('routes.views.get_device')
    def test_ttn_app_exception_handling(self, mock_get_device):
        """Test TTN app exception handling"""
        mock_get_device.side_effect = Exception("Processing error")

        request = self.factory.post(
            '/routes/ttn-app/',
            data=json.dumps(self.valid_payload),
            content_type='application/json',
            HTTP_AUTHORIZATION='valid_api_key'
        )
        
        response = ttn_app(request)

        # When get_device fails, it should return 500 and create a dropped packet
        self.assertEqual(response.status_code, 500)

    def test_ttn_app_payload_without_uplink_message(self):
        """Test TTN app with payload that has no uplink_message"""
        payload_without_uplink = {
            "end_device_ids": {
                "dev_eui": self.device.euid
            }
            # No uplink_message
        }
        
        request = self.factory.post(
            '/routes/ttn-app/',
            data=json.dumps(payload_without_uplink),
            content_type='application/json',
            HTTP_AUTHORIZATION=settings.TTN_API_KEY
        )
        
        with patch('routes.views.get_device') as mock_get_device:
            mock_get_device.return_value = self.device
            response = ttn_app(request)
        
        # Should still return 202 as it's accepted but just no uplink to process
        self.assertIn(response.status_code, [200,202])


@tag('unit')
class ProcessDropPacketUnitTestCase(BaseRoutesTestCase):
    """Unit tests for _process_drop_packet function"""

    def test_process_drop_packet_with_device_and_gateway(self):
        """Test _process_drop_packet with valid device and gateway"""
        payload = {
            "end_device_ids": {"dev_eui": self.device.euid},
            "uplink_message": {
                "rx_metadata": [{
                    "gateway_ids": {"eui": self.gateway.euid}
                }]
            }
        }

        dropped_packet = _process_drop_packet(payload, "Test exception")

        self.assertIsInstance(dropped_packet, DroppedPacket)
        self.assertEqual(dropped_packet.devi, self.device)
        self.assertEqual(dropped_packet.gate, self.gateway)
        self.assertEqual(dropped_packet.data, payload)
        self.assertEqual(dropped_packet.expt, "Test exception")

    def test_process_drop_packet_without_device(self):
        """Test _process_drop_packet with non-existent device"""
        payload = {
            "end_device_ids": {"dev_eui": "NONEXISTENT123"},
            "uplink_message": {
                "rx_metadata": [{
                    "gateway_ids": {"eui": self.gateway.euid}
                }]
            }
        }

        dropped_packet = _process_drop_packet(payload, "Test exception")

        self.assertIsInstance(dropped_packet, DroppedPacket)
        self.assertIsNone(dropped_packet.devi)
        self.assertEqual(dropped_packet.gate, self.gateway)

    def test_process_drop_packet_without_gateway(self):
        """Test _process_drop_packet with non-existent gateway"""
        payload = {
            "end_device_ids": {"dev_eui": self.device.euid},
            "uplink_message": {
                "rx_metadata": [{
                    "gateway_ids": {"eui": "NONEXISTENT123"}
                }]
            }
        }

        dropped_packet = _process_drop_packet(payload, "Test exception")

        self.assertIsInstance(dropped_packet, DroppedPacket)
        self.assertEqual(dropped_packet.devi, self.device)
        self.assertIsNone(dropped_packet.gate)

    def test_process_drop_packet_minimal_payload(self):
        """Test _process_drop_packet with minimal payload"""
        payload = {}

        dropped_packet = _process_drop_packet(payload, "Test exception")

        self.assertIsInstance(dropped_packet, DroppedPacket)
        self.assertIsNone(dropped_packet.devi)
        self.assertIsNone(dropped_packet.gate)
        self.assertEqual(dropped_packet.expt, "Test exception")

    @patch('routes.views.traceback.format_exc')
    def test_process_drop_packet_without_exception_message(self, mock_traceback):
        """Test _process_drop_packet without explicit exception message"""
        mock_traceback.return_value = "Traceback exception"
        payload = {"test": "data"}

        dropped_packet = _process_drop_packet(payload)

        self.assertEqual(dropped_packet.expt, "Traceback exception")
        mock_traceback.assert_called_once()

    def test_process_drop_packet_missing_rx_metadata(self):
        """Test _process_drop_packet with missing rx_metadata"""
        payload = {
            "end_device_ids": {"dev_eui": self.device.euid},
            "uplink_message": {}  # No rx_metadata
        }

        dropped_packet = _process_drop_packet(payload, "Test exception")

        self.assertEqual(dropped_packet.devi, self.device)
        self.assertIsNone(dropped_packet.gate)

    def test_process_drop_packet_empty_rx_metadata(self):
        """Test _process_drop_packet with empty rx_metadata"""
        payload = {
            "end_device_ids": {"dev_eui": self.device.euid},
            "uplink_message": {
                "rx_metadata": []  # Empty list
            }
        }

        dropped_packet = _process_drop_packet(payload, "Test exception")

        self.assertEqual(dropped_packet.devi, self.device)
        self.assertIsNone(dropped_packet.gate)


@tag('unit')
class ProcessUplinkUnitTestCase(BaseRoutesTestCase):
    """Unit tests for _process_uplink function"""

    def setUp(self):
        """Set up test data"""
        super().setUp()

        self.uplink_payload = {
            "uplink_message": {
                "f_cnt": 123,
                "rx_metadata": [{
                    "gateway_ids": {"eui": self.gateway.euid},
                    "rssi": -80,
                    "snr": 10.5,
                    "time": "2023-01-01T12:00:00Z",
                    "received_at": "2023-01-01T12:00:01Z",
                    "channel_index": 1
                }],
                "settings": {
                    "frequency": 868100000.0
                },
                "decoded_payload": {
                    "messages": [[
                        {
                            "type": "Air Temperature",
                            "measurementValue": 25.5
                        },
                        {
                            "type": "Battery",
                            "measurementValue": 85
                        },
                        {
                            "type": "Positioning Status",
                            "measurementValue": {"statusName": "GPS_FIXED"}
                        },
                        {
                            "type": "Event Status",
                            "measurementValue": [
                                {"eventName": "Motion event."},
                                {"eventName": "Shock event."}
                            ]
                        }
                    ]],
                    "payload": "base64encodeddata"
                }
            }
        }

    @patch('routes.views.get_device')
    def test_process_uplink_gateway_not_found(self, mock_get_device):
        """Test _process_uplink when gateway is not found"""
        mock_get_device.return_value = None

        with patch('routes.views._process_drop_packet') as mock_drop:
            _process_uplink(self.device, self.uplink_payload)

        mock_get_device.assert_called_once_with(self.gateway.euid)
        mock_drop.assert_called_once()

    @patch('routes.views.get_device')
    @patch('routes.views.is_event_within_work_shift')
    @patch('routes.views.make_attr_connections')
    @patch('routes.views.ttn_executor.submit')
    def test_process_uplink_successful_node_processing(self, mock_submit, mock_attr_conn,
                                                      mock_work_shift, mock_get_device):
        """Test successful _process_uplink for regular node"""
        mock_get_device.return_value = self.gateway
        mock_work_shift.return_value = True

        _process_uplink(self.device, self.uplink_payload)

        # Verify device attributes were updated
        self.assertEqual(self.device.attr["client"]["Frame Counter"], 123)
        self.assertEqual(self.device.attr["client"]["RSSI"], -80)
        self.assertEqual(self.device.attr["client"]["SNR"], 10.5)
        self.assertEqual(self.device.attr["client"]["Air Temperature"], 25.5)
        self.assertEqual(self.device.attr["client"]["Battery"], 85)
        self.assertEqual(self.device.attr["client"]["Positioning Status"], "GPS_FIXED")
        self.assertTrue(self.device.attr["client"]["Motion event."])
        self.assertTrue(self.device.attr["client"]["Shock event."])

        # Verify functions were called
        mock_attr_conn.assert_called_once_with(self.device)
        # Verify async tasks were submitted
        self.assertTrue(mock_submit.called)

    @patch('routes.views.get_device')
    @patch('routes.views.is_event_within_work_shift')
    @patch('routes.views.make_attr_connections')
    @patch('routes.views.ttn_executor.submit')
    def test_process_uplink_gateway_node_processing(self, mock_submit, mock_attr_conn,
                                                   mock_work_shift, mock_get_device):
        """Test _process_uplink for gateway node (name starts with GWN)"""
        mock_get_device.return_value = self.gateway
        mock_work_shift.return_value = False  # Outside work hours

        # Make device name start with GWN
        self.device.name = "GWN Test Device"

        _process_uplink(self.device, self.uplink_payload)

        # Verify basic processing was done
        mock_attr_conn.assert_called_once_with(self.device)
        # Verify async tasks were submitted
        self.assertTrue(mock_submit.called)

    def test_process_uplink_event_status_resets_old_events(self):
        """Test that Event Status processing resets old events"""
        # Set up device with existing events
        self.device.attr["client"]["Old event."] = True
        self.device.attr["client"]["Motion event."] = True

        # Payload with only Motion event
        payload = {
            "uplink_message": {
                "f_cnt": 123,
                "rx_metadata": [{
                    "gateway_ids": {"eui": self.gateway.euid},
                    "rssi": -80,
                    "snr": 10.5,
                    "time": "2023-01-01T12:00:00Z",
                    "received_at": "2023-01-01T12:00:01Z"
                }],
                "settings": {"frequency": 868100000.0},
                "decoded_payload": {
                    "messages": [[
                        {
                            "type": "Event Status",
                            "measurementValue": [
                                {"eventName": "Motion event."}
                            ]
                        }
                    ]]
                }
            }
        }

        with patch('routes.views.get_device') as mock_get_device:
            mock_get_device.return_value = self.gateway
            with patch('routes.views.is_event_within_work_shift'):
                with patch('routes.views.make_attr_connections'):
                    with patch('routes.views.ttn_executor.submit'):
                        _process_uplink(self.device, payload)

        # Motion event should still be True
        self.assertTrue(self.device.attr["client"]["Motion event."])
        # Old event should be reset to False
        self.assertFalse(self.device.attr["client"]["Old event."])

    def test_process_uplink_without_decoded_payload(self):
        """Test _process_uplink without decoded payload"""
        payload = {
            "uplink_message": {
                "f_cnt": 123,
                "rx_metadata": [{
                    "gateway_ids": {"eui": self.gateway.euid},
                    "rssi": -80,
                    "snr": 10.5,
                    "time": "2023-01-01T12:00:00Z",
                    "received_at": "2023-01-01T12:00:01Z"
                }],
                "settings": {"frequency": 868100000.0}
                # No decoded_payload
            }
        }

        with patch('routes.views.get_device') as mock_get_device:
            mock_get_device.return_value = self.gateway
            with patch('routes.views.ttn_executor.submit'):
                _process_uplink(self.device, payload)

        # Should still update basic attributes
        self.assertEqual(self.device.attr["client"]["Frame Counter"], 123)
        self.assertEqual(self.device.attr["client"]["RSSI"], -80)


@tag('integration')
class TTNAppIntegrationTestCase(BaseRoutesTestCase):
    """Integration tests for TTN app with real database interactions"""

    def setUp(self):
        """Set up test data"""
        super().setUp()
        self.factory = RequestFactory()

        self.valid_payload = {
            "end_device_ids": {
                "dev_eui": self.device.euid
            },
            "uplink_message": {
                "f_cnt": 123,
                "rx_metadata": [{
                    "gateway_ids": {"eui": self.gateway.euid},
                    "rssi": -80,
                    "snr": 10.5,
                    "time": "2023-01-01T12:00:00Z",
                    "received_at": "2023-01-01T12:00:01Z",
                    "channel_index": 1
                }],
                "settings": {
                    "frequency": 868100000.0
                },
                "decoded_payload": {
                    "messages": [[
                        {
                            "type": "Air Temperature",
                            "measurementValue": 25.5
                        }
                    ]],
                    "payload": "base64encodeddata"
                }
            }
        }

    @patch('routes.views.settings.TTN_API_KEY', 'valid_api_key')
    def test_ttn_app_creates_dropped_packet_on_error(self):
        """Test that TTN app creates dropped packet on processing error"""
        request = self.factory.post(
            '/routes/ttn-app/',
            data=json.dumps(self.valid_payload),
            content_type='application/json',
            HTTP_AUTHORIZATION='valid_api_key'
        )

        # Mock get_device to raise exception
        with patch('routes.views.get_device') as mock_get_device:
            mock_get_device.side_effect = Exception("Database error")

            response = ttn_app(request)

        self.assertEqual(response.status_code, 500)

        # Verify dropped packet was created
        dropped_packets = DroppedPacket.objects.all()
        self.assertEqual(len(dropped_packets), 1)
        self.assertEqual(dropped_packets[0].data, self.valid_payload)

    @patch('routes.views.settings.TTN_API_KEY', 'valid_api_key')
    def test_ttn_app_creates_uplink_packet_on_success(self):
        """Test that TTN app creates uplink packet on successful processing"""
        request = self.factory.post(
            '/routes/ttn-app/',
            data=json.dumps(self.valid_payload),
            content_type='application/json',
            HTTP_AUTHORIZATION='valid_api_key'
        )

        # Mock external dependencies and async processing
        with patch('routes.views.make_attr_connections'):
            with patch('routes.views.is_event_within_work_shift'):
                with patch('routes.views.ttn_executor.submit'):
                    response = ttn_app(request)

        self.assertEqual(response.status_code, 202)  # Async processing returns 202

        # Since processing is async, we can't directly verify uplink packet creation
        # The test verifies that the request was accepted for processing


@tag('e2e')
class TTNAppE2ETestCase(BaseRoutesTestCase):
    """End-to-end tests for complete TTN webhook processing workflow"""

    def setUp(self):
        """Set up test data"""
        super().setUp()
        self.factory = RequestFactory()

    @patch('routes.views.settings.TTN_API_KEY', 'valid_api_key')
    @patch('routes.views.UplinkPacket.objects.create')
    def test_complete_ttn_webhook_workflow(self, mock_create):
        """Test complete TTN webhook processing from request to database"""
        payload = {
            "end_device_ids": {
                "device_id": f"eui-{self.device.euid}",
                "application_ids": {
                    "application_id": "whiskershub"
                },
                "dev_eui": self.device.euid,
                "join_eui": self.device.euid,
                "dev_addr": "01889B41",
                "dev_eui": self.device.euid
            },
            "uplink_message": {
                "f_cnt": 456,
                "rx_metadata": [{
                    "gateway_ids": {
                        "gateway_id": "eui-"+self.gateway.euid,
                        "eui": self.gateway.euid,
                    },
                    "rssi": -75,
                    "snr": 12.0,
                    "time": "2023-01-01T14:00:00Z",
                    "received_at": "2023-01-01T14:00:01Z",
                    "channel_index": 2,
                }],
                "settings": {
                    "frequency": 868300000.0
                },
                "decoded_payload": {
                    "err": 0,
                    "messages": [[
                        {
                            "type": "Air Temperature",
                            "measurementId": "4097",
                            "timestamp": 1739859465000,
                            "measurementValue": 22.5,
                        },
                        {
                            "type": "Battery",
                            "measurementValue": 75,
                            "measurementId": "3000",
                            "timestamp": 1739859465000,
                        },
                    ]],
                    "payload": "110500000066b8576c01c9000038",
                    "f_cnt": 456,
                }
            }
        }

        request = self.factory.post(
            '/routes/ttn-app/',
            data=json.dumps(payload),
            content_type='application/json',
            HTTP_AUTHORIZATION='valid_api_key',        
        )  

        response = ttn_app(request)

        # Verify response
        self.assertEqual(response.status_code, 202)  # Async processing returns 202

        # Since processing is async, we can't directly verify device updates
        # The test verifies that the request was accepted for processing
        # In a real scenario, the async task would update the device attributes

        # Verify that the request was processed successfully
        self.assertEqual(response.status_code, 202)

        # Since UplinkPacket creation happens in async task, we can't directly verify it
        # The test verifies that the TTN webhook was accepted and processed
