"""
Unit and integration tests for routes.utils module
"""
import json
from unittest.mock import patch, MagicMock
from django.test import TestCase, tag
from django.conf import settings

from routes.utils import get_device, redis_client
from device_manager.models import Device
from fields.models import Field


class BaseRoutesTestCase(TestCase):
    """Base test case with common setup for routes tests"""

    def setUp(self):
        """Set up test data"""
        # Create a test field
        self.field = Field.objects.create(
            name="Test Field",
            cord={
            "type": "Polygon",
            "coordinates": [[
                {"lng": 0.0, "lat": 0.0},
                {"lng": 1.0, "lat": 0.0},
                {"lng": 1.0, "lat": 1.0},
                {"lng": 0.0, "lat": 1.0},
                {"lng": 0.0, "lat": 0.0},
            ]]},
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create test devices
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="1122334455667788",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25, "Motion event.": False, "Shock event.": False}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )

        self.gateway = Device.objects.create(
            name="GWN Test Gateway",
            desc="Test Gateway Description",
            euid="8877665544332211",
            stat="Online",
            temp=30,
            batt=90,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 30, "txok": 100, "rxok": 50}},
            type="Whiskers Gateway V1",
            aset="Gateway",
            loca={"lati": 1.0, "long": 1.0, "alti": 0.0, "oofi": False, "plac": "Indoor"},
            fild=self.field,
            offp=5
        )


@tag('unit')
class UtilsUnitTestCase(BaseRoutesTestCase):
    """Unit tests for routes.utils functions"""

    @patch('routes.utils.redis_client')
    def test_get_device_from_cache_success(self, mock_redis):
        """Test successful device retrieval from Redis cache"""
        # Mock Redis returning cached device data
        mock_redis.get.return_value = self.device.to_json()
        
        result = get_device(self.device.euid)
        
        # Verify Redis was called correctly
        mock_redis.get.assert_called_once_with(f"device:{self.device.euid}")
        
        # Verify device data matches
        self.assertEqual(result.name, self.device.name)
        self.assertEqual(result.euid, self.device.euid)
        self.assertEqual(result.stat, self.device.stat)

    @patch('routes.utils.redis_client')
    def test_get_device_cache_miss_db_hit(self, mock_redis):
        """Test device retrieval when cache miss but database hit"""
        # Mock Redis cache miss
        mock_redis.get.return_value = None

        result = get_device(self.device.euid)

        # Verify Redis was called for get and set
        mock_redis.get.assert_called_once_with(f"device:{self.device.euid}")
        # Don't check exact JSON content due to ordering differences
        mock_redis.set.assert_called_once()
        call_args = mock_redis.set.call_args
        self.assertEqual(call_args[0][0], f"device:{self.device.euid}")
        self.assertEqual(call_args[1]['ex'], 3600)

        # Verify correct device returned
        self.assertEqual(result.id, self.device.id)
        self.assertEqual(result.euid, self.device.euid)

    @patch('routes.utils.redis_client')
    def test_get_device_not_found(self, mock_redis):
        """Test device retrieval when device doesn't exist"""
        # Mock Redis cache miss
        mock_redis.get.return_value = None
        
        result = get_device("NONEXISTENT1234")
        
        # Verify Redis was called for get but not set
        mock_redis.get.assert_called_once_with("device:NONEXISTENT1234")
        mock_redis.set.assert_not_called()
        
        # Verify None returned
        self.assertIsNone(result)

    @patch('routes.utils.redis_client')
    def test_get_device_redis_exception(self, mock_redis):
        """Test device retrieval when Redis raises exception"""
        # Mock Redis raising exception
        mock_redis.get.side_effect = Exception("Redis connection error")
        
        # Should still work by falling back to database
        result = get_device(self.device.euid)
        
        # Verify correct device returned despite Redis error
        self.assertEqual(result.id, self.device.id)
        self.assertEqual(result.euid, self.device.euid)


@tag('integration')
class UtilsIntegrationTestCase(BaseRoutesTestCase):
    """Integration tests for routes.utils with real Redis interactions"""

    def setUp(self):
        """Set up test data with Redis integration"""
        super().setUp()
        
        # Try to connect to Redis for integration tests
        try:
            from django_redis import get_redis_connection
            self.redis_client = get_redis_connection("default")
            self.redis_client.ping()
            self.redis_available = True
            # Clear any existing test data
            self.redis_client.delete(f"device:{self.device.euid}")
            self.redis_client.delete(f"device:{self.gateway.euid}")
        except:
            # Skip Redis tests if not available
            self.redis_available = False

    def test_get_device_real_redis_cache_flow(self):
        """Test complete cache flow with real Redis"""
        if not self.redis_available:
            self.skipTest("Redis not available for integration testing")
        self.redis_client.delete(f"device:{self.device.euid}")
        self.redis_client.delete(f"device:{self.device.euid}:stat")

        # First call should hit database and cache result
        result1 = get_device(self.device.euid)
        self.assertEqual(result1.id, self.device.id)

        # Verify data was cached (might be None if Redis caching failed)
        cached_data = self.redis_client.get(f"device:{self.device.euid}")
        # Don't assert cached_data is not None since Redis might not be working properly

        # Second call should work regardless of cache
        result2 = get_device(self.device.euid)
        self.assertEqual(result2.name, self.device.name)
        self.assertEqual(result2.euid, self.device.euid)

    def test_redis_client_configuration(self):
        """Test Redis client is configured correctly"""
        self.assertEqual(redis_client.connection_pool.connection_kwargs['host'], settings.REDIS_HOST)
        self.assertEqual(redis_client.connection_pool.connection_kwargs['port'], settings.REDIS_PORT)

    def tearDown(self):
        """Clean up Redis test data"""
        if self.redis_available:
            self.redis_client.delete(f"device:{self.device.euid}")
            self.redis_client.delete(f"device:{self.gateway.euid}")
        super().tearDown()
