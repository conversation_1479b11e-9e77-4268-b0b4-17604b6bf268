"""
Unit tests for routes.decorators module
"""
from django.test import TestCase, RequestFactory, tag
from django.http import HttpResponseForbidden, HttpResponse
from unittest.mock import patch

from routes.decorators import local_only


def dummy_view(request):
    """Dummy view function for testing decorators"""
    return HttpResponse("Success")


@tag('unit')
class LocalOnlyDecoratorTestCase(TestCase):
    """Unit tests for local_only decorator"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
        self.decorated_view = local_only(dummy_view)

    def test_local_only_allows_172_ip(self):
        """Test that local_only decorator allows 172.x.x.x IPs"""
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = '**********'
        
        response = self.decorated_view(request)
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), "Success")

    def test_local_only_allows_172_subnet_variations(self):
        """Test that local_only decorator allows various 172.x.x.x IPs"""
        test_ips = [
            '*********',
            '**********', 
            '**************',
            '*************'
        ]
        
        for ip in test_ips:
            with self.subTest(ip=ip):
                request = self.factory.get('/')
                request.META['REMOTE_ADDR'] = ip
                
                response = self.decorated_view(request)
                
                self.assertEqual(response.status_code, 200)
                self.assertEqual(response.content.decode(), "Success")

    def test_local_only_blocks_non_172_ips(self):
        """Test that local_only decorator blocks non-172.x.x.x IPs"""
        blocked_ips = [
            '***********',
            '********',
            '127.0.0.1',
            '*******',
            '***********',
            '*********',  # Close but not 172
            '***************'  # Close but not 172
        ]
        
        for ip in blocked_ips:
            with self.subTest(ip=ip):
                request = self.factory.get('/')
                request.META['REMOTE_ADDR'] = ip
                
                response = self.decorated_view(request)
                
                self.assertIsInstance(response, HttpResponseForbidden)
                self.assertEqual(response.status_code, 403)
                self.assertEqual(response.content.decode(), "Access Denied")

    def test_local_only_handles_missing_remote_addr(self):
        """Test that local_only decorator handles missing REMOTE_ADDR"""
        request = self.factory.get('/')
        # Don't set REMOTE_ADDR
        
        response = self.decorated_view(request)
        
        # Should block when REMOTE_ADDR is None/missing
        self.assertIsInstance(response, HttpResponseForbidden)
        self.assertEqual(response.status_code, 403)

    def test_local_only_handles_none_remote_addr(self):
        """Test that local_only decorator handles None REMOTE_ADDR"""
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = None
        
        response = self.decorated_view(request)
        
        # Should block when REMOTE_ADDR is None
        self.assertIsInstance(response, HttpResponseForbidden)
        self.assertEqual(response.status_code, 403)

    @patch('builtins.print')
    def test_local_only_prints_access_denied(self, mock_print):
        """Test that local_only decorator prints 'Access Denied' when blocking"""
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = '***********'
        
        response = self.decorated_view(request)
        
        # Verify print was called
        mock_print.assert_called_once_with("Access Denied")
        self.assertIsInstance(response, HttpResponseForbidden)

    def test_local_only_preserves_view_args_kwargs(self):
        """Test that local_only decorator preserves view arguments"""
        def view_with_args(request, arg1, arg2, kwarg1=None):
            return HttpResponse(f"Args: {arg1}, {arg2}, Kwarg: {kwarg1}")
        
        decorated_view = local_only(view_with_args)
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = '**********'
        
        response = decorated_view(request, "test1", "test2", kwarg1="test3")
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), "Args: test1, test2, Kwarg: test3")

    def test_local_only_preserves_function_metadata(self):
        """Test that local_only decorator preserves function metadata"""
        def documented_view(request):
            """This is a documented view function"""
            return HttpResponse("Success")
        
        decorated_view = local_only(documented_view)
        
        # Check that functools.wraps preserved the metadata
        self.assertEqual(decorated_view.__name__, 'documented_view')
        self.assertEqual(decorated_view.__doc__, "This is a documented view function")

    def test_local_only_edge_case_empty_string_ip(self):
        """Test that local_only decorator handles empty string IP"""
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = ''
        
        response = self.decorated_view(request)
        
        # Should block empty string IP
        self.assertIsInstance(response, HttpResponseForbidden)
        self.assertEqual(response.status_code, 403)

    def test_local_only_edge_case_malformed_ip(self):
        """Test that local_only decorator handles malformed IP addresses"""
        malformed_ips = [
            'not.an.ip',
            '172',
            '172.',
            '172.invalid',
            'invalid.172.0.1'
        ]
        
        for ip in malformed_ips:
            with self.subTest(ip=ip):
                request = self.factory.get('/')
                request.META['REMOTE_ADDR'] = ip
                
                response = self.decorated_view(request)
                
                # Should block malformed IPs (they don't start with "172.")
                if not str(ip).startswith("172."):
                    self.assertIsInstance(response, HttpResponseForbidden)
                    self.assertEqual(response.status_code, 403)
