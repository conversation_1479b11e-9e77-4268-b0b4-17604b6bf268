import logging
import os
from concurrent.futures import Thread<PERSON>oolExecutor
from decouple import config
from django.db import transaction
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer

from device_manager.scripts import make_attr_connections
from notification_center.utils import (
    check_back_online,
    check_gateway_node,
    check_out_of_field,
    check_triggers,
    generate_global_events,
)
from device_manager.models import Device, attributes_templates
from packet_analyzer.models import UplinkPacket
from telemetry.scripts import generate_telemetries, batch_generate_telemetries
from fields.utils import is_event_within_work_shift

logger = logging.getLogger("app")

# Load TTN processing settings from environment
# First try to load from .env.ttn file if it exists
if os.path.exists('.env.ttn'):
    from dotenv import load_dotenv
    load_dotenv('.env.ttn')

# Create a dedicated thread pool for TTN processing
TTN_POOL_SIZE = config("TTN_POOL_SIZE", default=20, cast=int)  # Increased default from 10 to 20
logger.info(f"Initializing TTN processing thread pool with {TTN_POOL_SIZE} workers")

# Configure thread pool with optimized settings
ttn_executor = ThreadPoolExecutor(
    max_workers=TTN_POOL_SIZE,
    thread_name_prefix="ttn-worker"
)

def process_telemetry_async(device, telemetries):
    """
    Process telemetry data asynchronously in a background thread.
    """
    try:
        batch_generate_telemetries(device, telemetries)
    except Exception as e:
        logger.error(f"Error processing telemetry for device {device.id}: {e}")

def process_events_async(device, gateway, outside_work_hours):
    """
    Process events asynchronously in a background thread.
    """
    try:

        # Generate attribute connections and events
        make_attr_connections(device)

        if device.name.startswith("GWN"):
            check_gateway_node(device, gateway, outside_work_hours)
        else:
            generate_global_events(device)
            check_triggers(device, outside_work_hours)
            check_out_of_field(device)
    except Exception as e:
        logger.error(f"Error processing events for device {device.id}: {e}")

def send_websocket_update_async(device_id, data):
    """
    Send WebSocket updates asynchronously.
    """
    try:
        channel_layer = get_channel_layer()

        # Send to both the general device group and the device-specific group
        group_names = [
            f"device_{device_id}",  # For device-specific WebSocket connections
        ]

        for group_name in group_names:
            try:
                async_to_sync(channel_layer.group_send)(
                    group_name,
                    {
                        "type": "object_update",
                        "data": data,
                    }
                )
                logger.debug(f"WebSocket update sent to {group_name}")
            except Exception as group_error:
                logger.error(f"Error sending WebSocket update to group {group_name}: {group_error}")

    except Exception as e:
        logger.error(f"Error sending WebSocket update for device {device_id}: {e}")

def save_uplink_packet_async(device, gateway, uplink_pkt, rx_metadata):
    """
    Save uplink packet data asynchronously.
    """
    try:

        UplinkPacket.objects.create(
            devi=device,
            gate=gateway,
            data=uplink_pkt.get("decoded_payload", {}).get("payload", ""),
            deco=uplink_pkt,
            rssi=device.attr["client"].get("RSSI", 0),
            snr=device.attr["client"].get("SNR", 0),
            chan=rx_metadata.get("channel_index", 0),
            freq=uplink_pkt["settings"].get("frequency", 0.0),
            cont=device.attr["client"].get("Frame Counter", 0),
            txat=device.lupd,
            rxat=rx_metadata["received_at"],
        )
    except Exception as e:
        logger.error(f"Error saving uplink packet for device {device.id}: {e}")
