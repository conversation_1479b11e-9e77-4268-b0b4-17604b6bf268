import json
from auth.scripts import check_user_auth, fetch_devices
from channels.generic.websocket import WebsocketConsumer
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from device_manager.models import Device
from django.forms.models import model_to_dict
import logging
from django.http import Http404
from device_manager.utils.device_cache import DeviceCache

logger = logging.getLogger("app")


class DeviceConsumer(WebsocketConsumer):
    def connect(self):
        self.user = self.scope["user"]

        self.accept()

        # Only proceed if user is authenticated
        if not check_user_auth(self):
            return

    def disconnect(self, close_code):
        # Leave field group if it exists
        if hasattr(self, "devices"):
            devices = getattr(self, "devices", [])
            for device in devices:
                async_to_sync(get_channel_layer().group_discard)(
                    f"device_{device.id}", self.channel_name
                )

    def receive(self, text_data):
        fetch_devices(self, text_data)

        self.handle_subscription()
        self.send_initial_data()

    def handle_subscription(self):
        for device in self.devices:
            async_to_sync(get_channel_layer().group_add)(
                f"device_{device.id}", self.channel_name
            )

    def send_initial_data(self):
        devices_list = [device.to_dict() for device in self.devices]
        for device in devices_list:
            cached_device=DeviceCache().get_cached_device(device["id"])
            if cached_device:
                device["stat"] = cached_device.stat
                device["lupd"] = cached_device.lupd
                device["offp"] = cached_device.offp
        try:
            self.send(json.dumps(devices_list))
        except Exception as e:
            logger.error(f"Error sending initial device data: {e}")

    def object_update(self, event):
        try:
            self.send(text_data=json.dumps([event["data"]]))
        except Exception as e:
            logger.error(f"Error sending device update: {e}")


class DeviceDetailConsumer(WebsocketConsumer):
    def connect(self):
        self.user = self.scope["user"]
        self.device_id = self.scope["url_route"]["kwargs"]["device_id"]
        self.device_group_name = f"device_{self.device_id}"

        # Accept the connection
        self.accept()

        # Only proceed if user is authenticated
        if not check_user_auth(self):
            return

        # Try to get the device
        try:
            self.device = Device.objects.get(id=self.device_id)
            # Add to device group
            async_to_sync(get_channel_layer().group_add)(
                self.device_group_name, self.channel_name
            )
            # Send initial data
            self.send_initial_data()
        except Device.DoesNotExist:
            logger.error(f"Device with ID {self.device_id} not found")
            self.close(code=4002)  # Custom code for "object not found"
            return

    def disconnect(self, close_code):
        # Leave device group
        if hasattr(self, "device_group_name"):
            async_to_sync(get_channel_layer().group_discard)(
                self.device_group_name, self.channel_name
            )

    def receive(self, text_data):
        # This consumer doesn't need to handle incoming messages
        # It's primarily for sending updates about a specific device
        pass

    def send_initial_data(self):
        # Get the latest device data, preferring cached data if available
        device_data = self.device.to_dict()
        cached_device = DeviceCache().get_cached_device(self.device_id)
        if cached_device:
            device_data["stat"] = cached_device.stat
            device_data["lupd"] = cached_device.lupd
            device_data["offp"] = cached_device.offp

        # Send the device data
        try:
            self.send(json.dumps(device_data))
        except Exception as e:
            logger.error(f"Error sending initial device detail data for device {self.device_id}: {e}")

    def object_update(self, event):
        # Forward the update to the WebSocket
        try:
            self.send(text_data=json.dumps(event["data"]))
        except Exception as e:
            logger.error(f"Error sending device detail update for device {self.device_id}: {e}")
