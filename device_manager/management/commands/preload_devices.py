from django.core.management.base import BaseCommand
from django.utils import timezone
from device_manager.models import Device
from device_manager.utils.device_cache import DeviceCache

class Command(BaseCommand):
    help = "Preload all devices into Redis and set dynamic TTL for stat based on lupd + offp"

    def handle(self, *args, **options):
        now = timezone.now()
        count = 0

        devices = Device.objects.all()
        for device in devices:
            

            # Calculate dynamic TTL for stat
            if device.lupd:
                expire_time = device.lupd + timezone.timedelta(minutes=device.offp)
                ttl_seconds = int((expire_time - now).total_seconds())
                ttl_seconds = max(ttl_seconds, 10)  # prevent 0 or negative TTLs
            else:
                ttl_seconds = 60  # fallback
            DeviceCache().cache_device(device,ttl_seconds)
            print(f"Device {device.id} stat {device.stat} cached with TTL: {ttl_seconds} seconds")
            count += 1

        self.stdout.write(self.style.SUCCESS(f"Cached {count} devices with stat TTLs based on lupd + offp"))
