import logging
import traceback
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.utils import timezone
from datetime import datetime
from django.core.management.base import BaseCommand
from django.conf import settings
from django_redis import get_redis_connection
from device_manager.utils.device_cache import DeviceCache

logger = logging.getLogger("app")

class Command(BaseCommand):
    help = "Listens for Redis key expiration events and triggers handlers."
    r = get_redis_connection("default")
    device_cache = DeviceCache()

    def handle(self, *args, **kwargs):
        self.r.config_set("notify-keyspace-events", "Ex")  # Enable expiry events
        pubsub = self.r.pubsub()
        pubsub.psubscribe("__keyevent@0__:expired")

        self.stdout.write(self.style.SUCCESS("Listening for expired Redis keys..."))

        for message in pubsub.listen():
            try:
                if message["type"] == "pmessage":
                    expired_key = message["data"]
                    if isinstance(expired_key, bytes):  # Ensure it's bytes before decoding
                        expired_key = expired_key.decode("utf-8")
                    if expired_key.startswith("device:") and expired_key.endswith(":stat"):
                        device_id = expired_key.split(":")[1]
                        self.handle_stat_expiration(device_id)
            except Exception as e:
                from device_manager.management.commands.preload_devices import Command as PreloadDevicesCommand
                PreloadDevicesCommand().handle()
                self.stdout.write(self.style.ERROR(f"[ERROR]: Handling Expired Key: {traceback.format_exc()}"))

    def handle_stat_expiration(self, device_id):
        # Set stat to "Offline" in Redis
        cached = self.device_cache.get_cached_device(device_id)
        if not cached:
            print(f"Device {device_id} not found in Redis.")
            return

       # Extract values from cached JSON
        lupd_str = cached.lupd  # ISO format string
        offp = int(cached.offp)  # minutes

        # Convert string back to datetime
        lupd = datetime.fromisoformat(lupd_str)
        if timezone.is_naive(lupd):
            lupd = timezone.make_aware(lupd, timezone.get_current_timezone())
            lupd = lupd.astimezone(timezone.get_current_timezone())

        now = timezone.now()
        if timezone.is_naive(now):
            now = timezone.make_aware(now, timezone.get_current_timezone())
        stat = cached.stat
        if (now - timezone.timedelta(minutes=offp)) > lupd and stat != "Offline":
            # Set stat to offline in Redis
            cached.stat="Offline"
            self.device_cache.cache_device(cached, ttl=60)  # Optional long TTL to keep it around

            # Send WebSocket update
            channel_layer = get_channel_layer()
            group_name = f"device_{device_id}"
            async_to_sync(channel_layer.group_send)(
                group_name,
                {
                    "type": "object_update",
                    "data": cached.to_dict(),
                }
            )
            print(f"Device {device_id} marked Offline via WebSocket.")
        else:
            if cached.lupd:
                expire_time = lupd + timezone.timedelta(minutes=cached.offp)
                ttl_seconds = int((expire_time - now).total_seconds())
                ttl_seconds = max(ttl_seconds, 10)  # prevent 0 or negative TTLs
            else:
                ttl_seconds = 3600  # check again after 1 hour
            self.device_cache.cache_device(cached,ttl_seconds)

