from django.test import TestCase, Client, RequestFactory, tag
from django.urls import reverse
from django.contrib.auth.models import User
from unittest.mock import patch, MagicMock
import json

from device_manager.models import Device
from device_manager.utils.device_cache import DeviceCache
from fields.models import Field
from accounts.models import UserProfile
from notification_center.models import Event


class BaseDeviceViewTestCase(TestCase):
    """Base test case with common setup for device view tests"""

    def setUp(self):
        """Set up test data"""
        # Create test users with different roles
        self.admin_user = User.objects.create_user(
            username="admin_user",
            email="<EMAIL>",
            password="testpassword",
            is_superuser=True,
            first_name="Admin",
            last_name="User"
        )

        self.regular_user = User.objects.create_user(
            username="regular_user",
            email="<EMAIL>",
            password="testpassword",
            is_superuser=False,
            first_name="Regular",
            last_name="User"
        )

        # Create a test field
        self.field = Field.objects.create(
            name="Test Field",
            cord={"type": "Polygon", "coordinates": [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]},
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create test devices
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25, "Motion event.": False, "Shock event.": False, "Motionless event.": True}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )

        # Create user profiles
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            role="Admin",
            phon="12345678",
            titl="Admin Title",
            orgn="Test Organization"
        )
        self.admin_profile.devs.add(self.device)

        self.regular_profile = UserProfile.objects.create(
            user=self.regular_user,
            role="User",
            phon="87654321",
            titl="User Title",
            orgn="Test Organization"
        )
        self.regular_profile.devs.add(self.device)

        # Set up the client
        self.client = Client()
        self.factory = RequestFactory()


@tag('unit')
class DeviceViewUnitTestCase(BaseDeviceViewTestCase):
    """Unit tests for device views with mocked dependencies"""

    def test_device_list_view_admin_access(self):
        """Test that admin users can access device list view"""
        self.client.login(username="admin_user", password="testpassword")
        url = reverse("device_manager:list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, "device_manager/list.html")
        self.assertIn("list", response.context)

    def test_device_list_view_regular_user_access(self):
        """Test that regular users can access device list view"""
        self.client.login(username="regular_user", password="testpassword")
        url = reverse("device_manager:list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, "device_manager/list.html")

    def test_device_detail_view_authorized_access(self):
        """Test device detail view with authorized user"""
        self.client.login(username="admin_user", password="testpassword")
        url = reverse("device_manager:detail", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertIn("device", response.context)
        self.assertEqual(response.context["device"], self.device)

    def test_device_detail_view_unauthorized_access(self):
        """Test device detail view redirects unauthorized users"""
        # Create a user without device access
        unauthorized_user = User.objects.create_user(
            username="unauthorized",
            password="testpassword"
        )
        UserProfile.objects.create(
            user=unauthorized_user,
            role="User",
            phon="00000000",
            titl="Unauthorized",
            orgn="Test"
        )

        self.client.login(username="unauthorized", password="testpassword")
        url = reverse("device_manager:detail", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_device_update_view_get(self):
        """Test device update view GET request"""
        self.client.login(username="admin_user", password="testpassword")
        url = reverse("device_manager:edit", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, "device_manager/create.html")
        self.assertIn("device", response.context)
        self.assertEqual(response.context["device"], self.device)

    def test_device_create_view_get(self):
        """Test device create view GET request"""
        self.client.login(username="admin_user", password="testpassword")
        url = reverse("device_manager:create")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, "device_manager/create.html")

    def test_device_create_view_non_superuser_access(self):
        """Test that non-superusers cannot access device create view"""
        self.client.login(username="regular_user", password="testpassword")
        url = reverse("device_manager:create")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Should redirect

    @patch('device_manager.utils.device_cache.DeviceCache.get_cached_device')
    def test_toggle_hide_view(self, mock_get_cached_device):
        """Test the toggle_hide view functionality"""
        mock_get_cached_device.return_value = None
        self.client.login(username="admin_user", password="testpassword")

        url = reverse("device_manager:toggle_hide", args=[self.device.id])

        # Test hiding device
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect

        self.device.refresh_from_db()
        self.assertTrue(self.device.hidn)

        # Test unhiding device
        response = self.client.get(url)
        self.device.refresh_from_db()
        self.assertFalse(self.device.hidn)

        # Verify event was created
        events = Event.objects.filter(devi=self.device, type="Update")
        self.assertTrue(events.exists())

    @patch('device_manager.utils.device_cache.DeviceCache.get_cached_device')
    def test_toggle_maintenance_view_admin(self, mock_get_cached_device):
        """Test toggle_maintenance view with admin user"""
        mock_get_cached_device.return_value = None
        self.client.login(username="admin_user", password="testpassword")

        url = reverse("device_manager:toggle_maintenance", args=[self.device.id])

        # Test enabling maintenance mode
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect

        self.device.refresh_from_db()
        self.assertTrue(self.device.mntc)

        # Test disabling maintenance mode
        response = self.client.get(url)
        self.device.refresh_from_db()
        self.assertFalse(self.device.mntc)

        # Verify events were created
        events = Event.objects.filter(devi=self.device, type="Warning")
        self.assertTrue(events.exists())

    @patch('device_manager.utils.device_cache.DeviceCache.get_cached_device')
    def test_toggle_maintenance_view_regular_user(self, mock_get_cached_device):
        """Test toggle_maintenance view with regular user (should create info event)"""
        mock_get_cached_device.return_value = None
        self.client.login(username="regular_user", password="testpassword")

        url = reverse("device_manager:toggle_maintenance", args=[self.device.id])

        # Regular user should not be able to change maintenance mode
        original_mntc = self.device.mntc
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect

        self.device.refresh_from_db()
        self.assertEqual(self.device.mntc, original_mntc)  # Should not change

        # Should create an info event instead
        events = Event.objects.filter(devi=self.device, type="Info")
        self.assertTrue(events.exists())

    @patch('device_manager.utils.device_cache.DeviceCache.get_cached_device')
    def test_clear_status_view(self, mock_get_cached_device):
        """Test the clear_status view functionality"""
        mock_get_cached_device.return_value = None
        self.client.login(username="admin_user", password="testpassword")

        # Set device to non-default state
        self.device.stat = "Warning"
        self.device.actv = True
        self.device.attr["client"]["Motion event."] = True
        self.device.attr["client"]["Shock event."] = True
        self.device.attr["client"]["Motionless event."] = False
        self.device.save()

        url = reverse("device_manager:clear_status", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect

        # Verify status was cleared
        self.device.refresh_from_db()
        self.assertEqual(self.device.stat, "Online")
        self.assertFalse(self.device.actv)
        self.assertFalse(self.device.attr["client"]["Motion event."])
        self.assertFalse(self.device.attr["client"]["Shock event."])
        self.assertTrue(self.device.attr["client"]["Motionless event."])

        # Verify event was created
        events = Event.objects.filter(devi=self.device, type="Update")
        self.assertTrue(events.exists())

    @patch('device_manager.models.Device.objects')
    def test_clear_all_status_view(self, mock_objects):
        """Test the clear_all_status view functionality"""

        # Mock queryset with .all()
        mock_queryset = MagicMock()
        mock_queryset.all.return_value = [self.device]
        mock_objects.return_value = mock_queryset

        url = reverse("device_manager:clear_all_status")
        response = self.client.get(url)

        self.assertEqual(response.status_code, 302)
        self.device.refresh_from_db()
        self.assertEqual(self.device.stat, "Online")

    def test_delete_view_superuser(self):
        """Test delete view with superuser access"""
        self.client.login(username="admin_user", password="testpassword")

        # Create an event for the device
        Event.objects.create(
            devi=self.device,
            type="Update",
            desc="Test event"
        )

        initial_device_count = Device.objects.count()
        initial_event_count = Event.objects.count()

        url = reverse("device_manager:delete", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect

        # Verify device and events were deleted
        self.assertEqual(Device.objects.count(), initial_device_count - 1)
        self.assertEqual(Event.objects.count(), initial_event_count - 1)

    def test_delete_view_non_superuser(self):
        """Test delete view denies access to non-superusers"""
        self.client.login(username="regular_user", password="testpassword")

        url = reverse("device_manager:delete", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Should redirect (access denied)

    def test_generate_address_view(self):
        """Test generate_address view returns unique address"""
        self.client.login(username="admin_user", password="testpassword")

        url = reverse("device_manager:generate_address")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        # Should return JSON response
        self.assertEqual(response['Content-Type'], 'application/json')
        data = json.loads(response.content)
        self.assertIn('address', data)
        self.assertEqual(len(data['address']), 8)  # Should be 8 character hex string

    def test_generate_address_view_non_superuser(self):
        """Test generate_address view denies access to non-superusers"""
        self.client.login(username="regular_user", password="testpassword")

        url = reverse("device_manager:generate_address")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Should redirect (access denied)


@tag('integration')
class DeviceViewIntegrationTestCase(BaseDeviceViewTestCase):
    """Integration tests for device views with real cache interactions"""

    def setUp(self):
        """Set up test data with cache integration"""
        super().setUp()

        # Initialize device cache
        self.device_cache = DeviceCache()

        # Try to connect to Redis for integration tests
        try:
            from django_redis import get_redis_connection
            self.redis_client = get_redis_connection("default")
            self.redis_client.ping()
            self.redis_available = True
        except:
            # Skip Redis tests if not available
            self.redis_available = False
            self.redis_client = MagicMock()
            self.redis_client.get.return_value = None
            self.redis_client.set.return_value = True
            self.redis_client.delete.return_value = True

    def tearDown(self):
        """Clean up after tests"""
        if self.redis_available and hasattr(self, 'device'):
            try:
                self.redis_client.delete(f"device:{self.device.id}")
                self.redis_client.delete(f"device:{self.device.id}:stat")
            except:
                pass

    def test_device_list_view_with_cache_integration(self):
        """Test device list view integrates properly with cache"""
        self.client.login(username="admin_user", password="testpassword")

        # Cache the device first
        if self.redis_available:
            self.device_cache.cache_device(self.device)

        url = reverse("device_manager:list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertIn("list", response.context)

        # Verify device is in the list
        device_list = response.context["list"]
        self.assertTrue(any(d.id == self.device.id for d in device_list))

    def test_toggle_hide_with_cache_integration(self):
        """Test toggle_hide view integrates with cache"""
        self.client.login(username="admin_user", password="testpassword")

        url = reverse("device_manager:toggle_hide", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect

        # Refresh device from database
        self.device.refresh_from_db()
        self.assertTrue(self.device.hidn)

        # If Redis is available, verify cache was updated
        if self.redis_available:
            cached_json = self.redis_client.get(f"device:{self.device.id}")
            if cached_json:
                cached_data = json.loads(cached_json)
                self.assertTrue(cached_data["hidn"])

    def test_toggle_maintenance_with_cache_integration(self):
        """Test toggle_maintenance view integrates with cache"""
        self.client.login(username="admin_user", password="testpassword")

        url = reverse("device_manager:toggle_maintenance", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect

        # Refresh device from database
        self.device.refresh_from_db()
        self.assertTrue(self.device.mntc)

        # If Redis is available, verify cache was updated
        if self.redis_available:
            cached_json = self.redis_client.get(f"device:{self.device.id}")
            if cached_json:
                cached_data = json.loads(cached_json)
                self.assertTrue(cached_data["mntc"])

    def test_clear_status_with_cache_integration(self):
        """Test clear_status view integrates with cache"""
        self.client.login(username="admin_user", password="testpassword")

        # Set device to non-default state
        self.device.stat = "Warning"
        self.device.actv = True
        self.device.attr["client"]["Motion event."] = True
        self.device.attr["client"]["Shock event."] = True
        self.device.attr["client"]["Motionless event."] = False
        self.device.save()

        url = reverse("device_manager:clear_status", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect

        # Verify status was cleared
        self.device.refresh_from_db()
        self.assertEqual(self.device.stat, "Online")
        self.assertFalse(self.device.actv)

        # If Redis is available, verify cache was updated
        if self.redis_available:
            cached_json = self.redis_client.get(f"device:{self.device.id}")
            if cached_json:
                cached_data = json.loads(cached_json)
                self.assertEqual(cached_data["stat"], "Online")
                self.assertFalse(cached_data["actv"])


@tag('e2e')
class DeviceViewE2ETestCase(BaseDeviceViewTestCase):
    """End-to-end tests for complete device management workflows"""

    def test_complete_device_lifecycle_workflow(self):
        """Test complete device management workflow from creation to deletion"""
        self.client.login(username="admin_user", password="testpassword")

        # Test device creation workflow
        create_url = reverse("device_manager:create")
        create_response = self.client.get(create_url)
        self.assertEqual(create_response.status_code, 200)

        # Test device detail access
        detail_url = reverse("device_manager:detail", args=[self.device.id])
        detail_response = self.client.get(detail_url)
        self.assertEqual(detail_response.status_code, 200)

        # Test device update workflow
        update_url = reverse("device_manager:edit", args=[self.device.id])
        update_response = self.client.get(update_url)
        self.assertEqual(update_response.status_code, 200)

        # Test device status management
        hide_url = reverse("device_manager:toggle_hide", args=[self.device.id])
        hide_response = self.client.get(hide_url)
        self.assertEqual(hide_response.status_code, 302)

        maintenance_url = reverse("device_manager:toggle_maintenance", args=[self.device.id])
        maintenance_response = self.client.get(maintenance_url)
        self.assertEqual(maintenance_response.status_code, 302)

        clear_status_url = reverse("device_manager:clear_status", args=[self.device.id])
        clear_response = self.client.get(clear_status_url)
        self.assertEqual(clear_response.status_code, 302)

        # Test device deletion
        delete_url = reverse("device_manager:delete", args=[self.device.id])
        delete_response = self.client.get(delete_url)
        self.assertEqual(delete_response.status_code, 302)

        # Verify device was deleted
        self.assertFalse(Device.objects.filter(id=self.device.id).exists())

    def test_user_permission_workflow(self):
        """Test complete workflow with different user permission levels"""
        # Test admin user workflow
        self.client.login(username="admin_user", password="testpassword")

        # Admin should access all views
        list_url = reverse("device_manager:list")
        self.assertEqual(self.client.get(list_url).status_code, 200)

        create_url = reverse("device_manager:create")
        self.assertEqual(self.client.get(create_url).status_code, 200)

        generate_url = reverse("device_manager:generate_address")
        self.assertEqual(self.client.get(generate_url).status_code, 200)

        # Test regular user workflow
        self.client.login(username="regular_user", password="testpassword")

        # Regular user should access limited views
        self.assertEqual(self.client.get(list_url).status_code, 200)
        self.assertEqual(self.client.get(create_url).status_code, 302)  # Redirect
        self.assertEqual(self.client.get(generate_url).status_code, 302)  # Redirect
