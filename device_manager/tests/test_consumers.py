from django.test import TestCase, tag
from django.contrib.auth.models import User
from unittest.mock import patch, MagicMock, AsyncMock
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async
import json

from device_manager.models import Device
from device_manager.consumers import DeviceConsumer, DeviceDetailConsumer
from fields.models import Field
from accounts.models import UserProfile


class BaseConsumerTestCase(TestCase):
    """Base test case with common setup for consumer tests"""

    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword",
            is_superuser=True
        )

        # Create test field
        self.field = Field.objects.create(
            name="Test Field",
            cord={"type": "Polygon", "coordinates": [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]},
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create test device
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25, "Motion event.": False, "Shock event.": False, "Motionless event.": True}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )

        # Create user profile
        self.profile = UserProfile.objects.create(
            user=self.user,
            role="Admin",
            phon="12345678",
            titl="Test Title",
            orgn="Test Organization"
        )
        self.profile.devs.add(self.device)


@tag('unit')
class DeviceConsumerUnitTestCase(BaseConsumerTestCase):
    """Unit tests for DeviceConsumer"""

    def setUp(self):
        super().setUp()
        self.consumer = DeviceConsumer()
        self.consumer.scope = {
            'user': self.user,
            'type': 'websocket',
            'path': '/ws/devices/',
        }
        self.consumer.channel_name = 'test-channel'

    @patch('device_manager.consumers.check_user_auth')
    def test_connect_authenticated_user(self, mock_check_auth):
        """Test connect method with authenticated user"""
        mock_check_auth.return_value = True
        
        with patch.object(self.consumer, 'accept') as mock_accept:
            self.consumer.connect()
            mock_accept.assert_called_once()
            mock_check_auth.assert_called_once_with(self.consumer)

    @patch('device_manager.consumers.check_user_auth')
    def test_connect_unauthenticated_user(self, mock_check_auth):
        """Test connect method with unauthenticated user"""
        mock_check_auth.return_value = False
        
        with patch.object(self.consumer, 'accept') as mock_accept:
            self.consumer.connect()
            mock_accept.assert_called_once()
            mock_check_auth.assert_called_once_with(self.consumer)

    @patch('device_manager.consumers.get_channel_layer')
    @patch('device_manager.consumers.async_to_sync')
    def test_disconnect_with_devices(self, mock_async_to_sync, mock_get_channel_layer):
        """Test disconnect method when devices are present"""
        # Set up devices attribute
        self.consumer.devices = [self.device]
        
        # Mock channel layer
        mock_channel_layer = MagicMock()
        mock_get_channel_layer.return_value = mock_channel_layer
        mock_group_discard = MagicMock()
        mock_async_to_sync.return_value = mock_group_discard
        
        self.consumer.disconnect(1000)
        
        # Verify group_discard was called for each device
        mock_async_to_sync.assert_called_with(mock_channel_layer.group_discard)
        mock_group_discard.assert_called_with(f"device_{self.device.id}", self.consumer.channel_name)

    def test_disconnect_without_devices(self):
        """Test disconnect method when no devices are present"""
        # Should not raise any errors
        self.consumer.disconnect(1000)

    @patch('device_manager.consumers.fetch_devices')
    def test_receive_calls_fetch_devices(self, mock_fetch_devices):
        """Test receive method calls fetch_devices"""
        self.consumer.devices = [self.device]
        
        with patch.object(self.consumer, 'handle_subscription') as mock_handle_sub, \
             patch.object(self.consumer, 'send_initial_data') as mock_send_data:
            
            test_data = '{"test": "data"}'
            self.consumer.receive(test_data)
            
            mock_fetch_devices.assert_called_once_with(self.consumer, test_data)
            mock_handle_sub.assert_called_once()
            mock_send_data.assert_called_once()

    @patch('device_manager.consumers.get_channel_layer')
    @patch('device_manager.consumers.async_to_sync')
    def test_handle_subscription(self, mock_async_to_sync, mock_get_channel_layer):
        """Test handle_subscription method"""
        self.consumer.devices = [self.device]
        
        mock_channel_layer = MagicMock()
        mock_get_channel_layer.return_value = mock_channel_layer
        mock_group_add = MagicMock()
        mock_async_to_sync.return_value = mock_group_add
        
        self.consumer.handle_subscription()
        
        mock_async_to_sync.assert_called_with(mock_channel_layer.group_add)
        mock_group_add.assert_called_with(f"device_{self.device.id}", self.consumer.channel_name)

    @patch('device_manager.consumers.DeviceCache')
    def test_send_initial_data_with_cached_device(self, mock_device_cache_class):
        """Test send_initial_data with cached device data"""
        self.consumer.devices = [self.device]
        
        # Mock cached device
        mock_cached_device = MagicMock()
        mock_cached_device.stat = "Cached Status"
        mock_cached_device.lupd = "2023-01-01T00:00:00Z"
        mock_cached_device.offp = 15
        
        mock_cache_instance = MagicMock()
        mock_cache_instance.get_cached_device.return_value = mock_cached_device
        mock_device_cache_class.return_value = mock_cache_instance
        
        with patch.object(self.consumer, 'send') as mock_send:
            self.consumer.send_initial_data()
            
            # Verify send was called with device data
            mock_send.assert_called_once()
            sent_data = json.loads(mock_send.call_args[0][0])
            self.assertEqual(len(sent_data), 1)
            self.assertEqual(sent_data[0]['stat'], "Cached Status")
            self.assertEqual(sent_data[0]['offp'], 15)

    @patch('device_manager.consumers.DeviceCache')
    def test_send_initial_data_without_cached_device(self, mock_device_cache_class):
        """Test send_initial_data without cached device data"""
        self.consumer.devices = [self.device]
        
        mock_cache_instance = MagicMock()
        mock_cache_instance.get_cached_device.return_value = None
        mock_device_cache_class.return_value = mock_cache_instance
        
        with patch.object(self.consumer, 'send') as mock_send:
            self.consumer.send_initial_data()
            
            mock_send.assert_called_once()
            sent_data = json.loads(mock_send.call_args[0][0])
            self.assertEqual(len(sent_data), 1)
            self.assertEqual(sent_data[0]['name'], self.device.name)

    @patch('device_manager.consumers.logger')
    def test_send_initial_data_exception_handling(self, mock_logger):
        """Test send_initial_data handles exceptions properly"""
        self.consumer.devices = [self.device]
        
        with patch.object(self.consumer, 'send', side_effect=Exception("Test error")):
            self.consumer.send_initial_data()
            
            mock_logger.error.assert_called_once()
            self.assertIn("Error sending initial device data", mock_logger.error.call_args[0][0])

    def test_object_update_success(self):
        """Test object_update method with successful send"""
        event = {"data": {"id": 1, "name": "Updated Device"}}
        
        with patch.object(self.consumer, 'send') as mock_send:
            self.consumer.object_update(event)
            
            mock_send.assert_called_once_with(text_data=json.dumps([event["data"]]))

    @patch('device_manager.consumers.logger')
    def test_object_update_exception_handling(self, mock_logger):
        """Test object_update handles exceptions properly"""
        event = {"data": {"id": 1, "name": "Updated Device"}}
        
        with patch.object(self.consumer, 'send', side_effect=Exception("Test error")):
            self.consumer.object_update(event)
            
            mock_logger.error.assert_called_once()
            self.assertIn("Error sending device update", mock_logger.error.call_args[0][0])


@tag('unit')
class DeviceDetailConsumerUnitTestCase(BaseConsumerTestCase):
    """Unit tests for DeviceDetailConsumer"""

    def setUp(self):
        super().setUp()
        self.consumer = DeviceDetailConsumer()
        self.consumer.scope = {
            'user': self.user,
            'type': 'websocket',
            'path': f'/ws/device/{self.device.id}/',
            'url_route': {'kwargs': {'device_id': str(self.device.id)}}
        }
        self.consumer.channel_name = 'test-detail-channel'

    @patch('device_manager.consumers.check_user_auth')
    @patch('device_manager.consumers.get_channel_layer')
    @patch('device_manager.consumers.async_to_sync')
    def test_connect_success(self, mock_async_to_sync, mock_get_channel_layer, mock_check_auth):
        """Test successful connection to device detail consumer"""
        mock_check_auth.return_value = True
        mock_channel_layer = MagicMock()
        mock_get_channel_layer.return_value = mock_channel_layer
        mock_group_add = MagicMock()
        mock_async_to_sync.return_value = mock_group_add
        
        with patch.object(self.consumer, 'accept') as mock_accept, \
             patch.object(self.consumer, 'send_initial_data') as mock_send_initial:
            
            self.consumer.connect()
            
            mock_accept.assert_called_once()
            mock_check_auth.assert_called_once_with(self.consumer)
            mock_send_initial.assert_called_once()
            self.assertEqual(self.consumer.device.id, self.device.id)

    @patch('device_manager.consumers.check_user_auth')
    @patch('device_manager.consumers.logger')
    def test_connect_device_not_found(self, mock_logger, mock_check_auth):
        """Test connection when device doesn't exist"""
        mock_check_auth.return_value = True
        
        # Set invalid device ID
        self.consumer.scope['url_route']['kwargs']['device_id'] = '99999'
        
        with patch.object(self.consumer, 'accept') as mock_accept, \
             patch.object(self.consumer, 'close') as mock_close:
            
            self.consumer.connect()
            
            mock_accept.assert_called_once()
            mock_close.assert_called_once_with(code=4002)
            mock_logger.error.assert_called_once()

    @patch('device_manager.consumers.check_user_auth')
    def test_connect_unauthenticated(self, mock_check_auth):
        """Test connection with unauthenticated user"""
        mock_check_auth.return_value = False
        
        with patch.object(self.consumer, 'accept') as mock_accept:
            self.consumer.connect()
            
            mock_accept.assert_called_once()
            mock_check_auth.assert_called_once_with(self.consumer)

    @patch('device_manager.consumers.get_channel_layer')
    @patch('device_manager.consumers.async_to_sync')
    def test_disconnect(self, mock_async_to_sync, mock_get_channel_layer):
        """Test disconnect method"""
        self.consumer.device_group_name = f"device_{self.device.id}"
        
        mock_channel_layer = MagicMock()
        mock_get_channel_layer.return_value = mock_channel_layer
        mock_group_discard = MagicMock()
        mock_async_to_sync.return_value = mock_group_discard
        
        self.consumer.disconnect(1000)
        
        mock_async_to_sync.assert_called_with(mock_channel_layer.group_discard)
        mock_group_discard.assert_called_with(self.consumer.device_group_name, self.consumer.channel_name)

    def test_receive(self):
        """Test receive method (should do nothing)"""
        # This method should pass without doing anything
        self.consumer.receive('{"test": "data"}')

    @patch('device_manager.consumers.DeviceCache')
    def test_send_initial_data_with_cache(self, mock_device_cache_class):
        """Test send_initial_data with cached device"""
        self.consumer.device = self.device
        self.consumer.device_id = self.device.id
        
        # Mock cached device
        mock_cached_device = MagicMock()
        mock_cached_device.stat = "Cached Status"
        mock_cached_device.lupd = "2023-01-01T00:00:00Z"
        mock_cached_device.offp = 20
        
        mock_cache_instance = MagicMock()
        mock_cache_instance.get_cached_device.return_value = mock_cached_device
        mock_device_cache_class.return_value = mock_cache_instance
        
        with patch.object(self.consumer, 'send') as mock_send:
            self.consumer.send_initial_data()
            
            mock_send.assert_called_once()
            sent_data = json.loads(mock_send.call_args[0][0])
            self.assertEqual(sent_data['stat'], "Cached Status")
            self.assertEqual(sent_data['offp'], 20)

    @patch('device_manager.consumers.logger')
    def test_send_initial_data_exception(self, mock_logger):
        """Test send_initial_data exception handling"""
        self.consumer.device = self.device
        self.consumer.device_id = self.device.id
        
        with patch.object(self.consumer, 'send', side_effect=Exception("Test error")):
            self.consumer.send_initial_data()
            
            mock_logger.error.assert_called_once()
            self.assertIn("Error sending initial device detail data", mock_logger.error.call_args[0][0])

    def test_object_update_success(self):
        """Test object_update method"""
        self.consumer.device_id = self.device.id
        event = {"data": {"id": self.device.id, "name": "Updated Device"}}
        
        with patch.object(self.consumer, 'send') as mock_send:
            self.consumer.object_update(event)
            
            mock_send.assert_called_once_with(text_data=json.dumps(event["data"]))

    @patch('device_manager.consumers.logger')
    def test_object_update_exception(self, mock_logger):
        """Test object_update exception handling"""
        self.consumer.device_id = self.device.id
        event = {"data": {"id": self.device.id, "name": "Updated Device"}}
        
        with patch.object(self.consumer, 'send', side_effect=Exception("Test error")):
            self.consumer.object_update(event)
            
            mock_logger.error.assert_called_once()
            self.assertIn("Error sending device detail update", mock_logger.error.call_args[0][0])


@tag('integration')
class ConsumerIntegrationTestCase(BaseConsumerTestCase):
    """Integration tests for consumers"""

    def test_device_consumer_integration(self):
        """Test DeviceConsumer integration with real data"""
        consumer = DeviceConsumer()
        consumer.scope = {'user': self.user}
        consumer.channel_name = 'test-integration-channel'
        
        # Test that consumer can be instantiated and basic methods work
        self.assertIsInstance(consumer, DeviceConsumer)
        self.assertEqual(consumer.scope['user'], self.user)

    def test_device_detail_consumer_integration(self):
        """Test DeviceDetailConsumer integration with real data"""
        consumer = DeviceDetailConsumer()
        consumer.scope = {
            'user': self.user,
            'url_route': {'kwargs': {'device_id': str(self.device.id)}}
        }
        consumer.channel_name = 'test-detail-integration-channel'
        
        # Test that consumer can be instantiated
        self.assertIsInstance(consumer, DeviceDetailConsumer)
        self.assertEqual(consumer.scope['user'], self.user)
