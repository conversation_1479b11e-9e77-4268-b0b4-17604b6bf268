from django.test import TestCase, tag
from unittest.mock import patch, MagicMock

from device_manager.models import Device
from device_manager.scripts import make_attr_connections
from fields.models import Field


class BaseScriptsTestCase(TestCase):
    """Base test case with common setup for scripts tests"""

    def setUp(self):
        """Set up test data"""
        # Create test field
        self.field = Field.objects.create(
            name="Test Field",
            cord={"type": "Polygon", "coordinates": [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]},
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )


@tag('unit')
class MakeAttrConnectionsUnitTestCase(BaseScriptsTestCase):
    """Unit tests for make_attr_connections function"""

    def setUp(self):
        super().setUp()
        
        # Mock attributes_templates for testing
        self.mock_templates = {
            "Whiskers Node V1": {
                "conc": {
                    "temp": "temp",
                    "batt": "battery_level",
                    "loca": {
                        "lati": "latitude",
                        "long": "longitude"
                    },
                    "actv": "motion_detected"
                }
            },
            "Whiskers Node V2": {
                "conc": {
                    "temp": "temperature",
                    "chrg": "charging_status",
                    "stat": "device_status"
                }
            }
        }

    @patch('device_manager.scripts.attributes_templates')
    def test_make_attr_connections_simple_attributes(self, mock_templates):
        """Test make_attr_connections with simple attribute mappings"""
        mock_templates.__getitem__.side_effect = self.mock_templates.__getitem__
        
        # Create device with client attributes
        device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=0,  # Will be updated by function
            batt=0,  # Will be updated by function
            chrg=False,
            actv=False,  # Will be updated by function
            mntc=False,
            hidn=False,
            attr={
                "client": {
                    "temp": 25,
                    "battery_level": 80,
                    "motion_detected": True
                }
            },
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )
        
        # Call the function
        make_attr_connections(device)
        
        # Verify attributes were set correctly
        self.assertEqual(device.temp, 25)
        self.assertEqual(device.batt, 80)
        self.assertEqual(device.actv, True)

    @patch('device_manager.scripts.attributes_templates')
    def test_make_attr_connections_nested_attributes(self, mock_templates):
        """Test make_attr_connections with nested attribute mappings"""
        mock_templates.__getitem__.side_effect = self.mock_templates.__getitem__
        
        # Create device with nested location data
        device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={
                "client": {
                    "latitude": 12.345,
                    "longitude": 67.890
                }
            },
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )
        
        # Call the function
        make_attr_connections(device)
        
        # Verify nested attributes were set correctly
        self.assertEqual(device.loca["lati"], 12.345)
        self.assertEqual(device.loca["long"], 67.890)

    @patch('device_manager.scripts.attributes_templates')
    def test_make_attr_connections_missing_client_attribute(self, mock_templates):
        """Test make_attr_connections when client attribute is missing"""
        mock_templates.__getitem__.side_effect = self.mock_templates.__getitem__
        
        # Create device without some expected client attributes
        device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=0,
            batt=50,  # Initial value
            chrg=False,
            actv=False,
            mntc=False,
            hidn=False,
            attr={
                "client": {
                    "temp": 25
                    # Missing "battery_level" and "motion_detected"
                }
            },
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )
        
        original_batt = device.batt
        original_actv = device.actv
        
        # Call the function
        make_attr_connections(device)
        
        # Verify only available attributes were set
        self.assertEqual(device.temp, 25)
        self.assertEqual(device.batt, original_batt)  # Should remain unchanged
        self.assertEqual(device.actv, original_actv)  # Should remain unchanged

    @patch('device_manager.scripts.attributes_templates')
    def test_make_attr_connections_missing_nested_attribute(self, mock_templates):
        """Test make_attr_connections when nested client attribute is missing"""
        mock_templates.__getitem__.side_effect = self.mock_templates.__getitem__
        
        # Create device without some nested client attributes
        device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={
                "client": {
                    "latitude": 12.345
                    # Missing "longitude"
                }
            },
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 5.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )
        
        original_long = device.loca["long"]
        
        # Call the function
        make_attr_connections(device)
        
        # Verify only available nested attributes were set
        self.assertEqual(device.loca["lati"], 12.345)
        self.assertEqual(device.loca["long"], original_long)  # Should remain unchanged

    @patch('device_manager.scripts.attributes_templates')
    def test_make_attr_connections_empty_client_attributes(self, mock_templates):
        """Test make_attr_connections with empty client attributes"""
        mock_templates.__getitem__.side_effect = self.mock_templates.__getitem__
        
        # Create device with empty client attributes
        device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=10,
            batt=50,
            chrg=False,
            actv=False,
            mntc=False,
            hidn=False,
            attr={"client": {}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 1.0, "long": 2.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )
        
        original_temp = device.temp
        original_batt = device.batt
        original_actv = device.actv
        original_loca = device.loca.copy()
        
        # Call the function
        make_attr_connections(device)
        
        # Verify no attributes were changed
        self.assertEqual(device.temp, original_temp)
        self.assertEqual(device.batt, original_batt)
        self.assertEqual(device.actv, original_actv)
        self.assertEqual(device.loca, original_loca)

    @patch('device_manager.scripts.attributes_templates')
    def test_make_attr_connections_different_device_type(self, mock_templates):
        """Test make_attr_connections with different device type"""
        mock_templates.__getitem__.side_effect = self.mock_templates.__getitem__
        
        # Create device with different type
        device = Device.objects.create(
            name="Test Device V2",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=0,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={
                "client": {
                    "temperature": 30,
                    "charging_status": True,
                    "device_status": "Warning"
                }
            },
            type="Whiskers Node V2",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )
        
        # Call the function
        make_attr_connections(device)
        
        # Verify attributes were set according to V2 template
        self.assertEqual(device.temp, 30)
        self.assertEqual(device.chrg, True)
        self.assertEqual(device.stat, "Warning")

    @patch('device_manager.scripts.attributes_templates')
    def test_make_attr_connections_mixed_attribute_types(self, mock_templates):
        """Test make_attr_connections with mixed simple and nested attributes"""
        # Create a template with both simple and nested mappings
        mixed_template = {
            "Mixed Device": {
                "conc": {
                    "temp": "temperature",  # Simple mapping
                    "loca": {  # Nested mapping
                        "lati": "lat",
                        "long": "lng"
                    },
                    "batt": "battery_percent",  # Simple mapping
                    "attr": {  # Nested mapping to attr field
                        "custom_field": "custom_value"
                    }
                }
            }
        }
        
        mock_templates.__getitem__.side_effect = mixed_template.__getitem__
        
        device = Device.objects.create(
            name="Mixed Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=0,
            batt=0,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={
                "client": {
                    "temperature": 35,
                    "lat": 45.678,
                    "lng": 123.456,
                    "battery_percent": 90,
                    "custom_value": "test_value"
                },
                "custom_field": "initial_value"
            },
            type="Mixed Device",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )
        
        # Call the function
        make_attr_connections(device)
        
        # Verify all mappings worked correctly
        self.assertEqual(device.temp, 35)
        self.assertEqual(device.batt, 90)
        self.assertEqual(device.loca["lati"], 45.678)
        self.assertEqual(device.loca["long"], 123.456)
        self.assertEqual(device.attr["custom_field"], "test_value")

    @patch('device_manager.scripts.attributes_templates')
    def test_make_attr_connections_no_client_attr(self, mock_templates):
        """Test make_attr_connections when device has no client attributes"""
        mock_templates.__getitem__.side_effect = self.mock_templates.__getitem__
        
        # Create device without client attributes
        device = Device.objects.create(
            name="No Client Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=15,
            batt=60,
            chrg=False,
            actv=False,
            mntc=False,
            hidn=False,
            attr={},  # No client key
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )
        
        original_temp = device.temp
        original_batt = device.batt
        
        # This should raise a KeyError when trying to access device.attr["client"]
        with self.assertRaises(KeyError):
            make_attr_connections(device)

    def test_make_attr_connections_function_signature(self):
        """Test that make_attr_connections function has correct signature"""
        import inspect
        
        sig = inspect.signature(make_attr_connections)
        params = list(sig.parameters.keys())
        
        # Should have one parameter named 'device'
        self.assertEqual(len(params), 1)
        self.assertEqual(params[0], 'device')
        
        # Parameter should have type annotation
        device_param = sig.parameters['device']
        self.assertEqual(device_param.annotation, Device)


@tag('integration')
class ScriptsIntegrationTestCase(BaseScriptsTestCase):
    """Integration tests for scripts module"""

    def test_make_attr_connections_with_real_templates(self):
        """Test make_attr_connections with real attributes_templates"""
        # This test uses the actual attributes_templates from the models
        from device_manager.models import attributes_templates
        
        # Check if we have real templates to test with
        if "Whiskers Node V1" in attributes_templates:
            device = Device.objects.create(
                name="Real Template Device",
                desc="Test Description",
                euid="ABCDEF0123456789",
                stat="Online",
                temp=0,
                batt=0,
                chrg=False,
                actv=False,
                mntc=False,
                hidn=False,
                attr={"client": {"temp": 25}},  # Minimal client data
                type="Whiskers Node V1",
                aset="Battery",
                loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
                fild=self.field,
                offp=10
            )
            
            # Should not raise any exceptions
            try:
                make_attr_connections(device)
            except Exception as e:
                self.fail(f"make_attr_connections raised an exception with real templates: {e}")

    def test_scripts_module_imports(self):
        """Test that scripts module imports work correctly"""
        from device_manager.scripts import make_attr_connections
        from device_manager.models import Device, attributes_templates
        
        # Verify imports are successful
        self.assertTrue(callable(make_attr_connections))
        self.assertTrue(hasattr(Device, '__name__'))
        self.assertIsInstance(attributes_templates, dict)

    def test_make_attr_connections_performance(self):
        """Test make_attr_connections performance with multiple devices"""
        import time

        # Mock templates for performance test
        mock_templates = {
            "Whiskers Node V1": {
                "conc": {
                    "temp": "temp"
                }
            }
        }

        # Create multiple devices
        devices = []
        for i in range(10):
            device = Device.objects.create(
                name=f"Performance Device {i}",
                desc="Performance Test",
                euid=f"PERF{i:012d}",
                stat="Online",
                temp=0,
                batt=0,
                chrg=False,
                actv=False,
                mntc=False,
                hidn=False,
                attr={"client": {"temp": 20 + i}},
                type="Whiskers Node V1",
                aset="Battery",
                loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
                fild=self.field,
                offp=10
            )
            devices.append(device)

        # Measure execution time
        start_time = time.time()

        with patch('device_manager.scripts.attributes_templates', mock_templates):
            for device in devices:
                make_attr_connections(device)

        end_time = time.time()
        execution_time = end_time - start_time

        # Should complete within reasonable time (adjust threshold as needed)
        self.assertLess(execution_time, 1.0, "Function took too long to execute")
