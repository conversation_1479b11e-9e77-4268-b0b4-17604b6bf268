from django.test import TestCase, tag

from device_manager.models import get_device_attrs_template, get_location_template


@tag('unit')
class DeviceUtilityFunctionsUnitTestCase(TestCase):
    """Unit tests for the utility functions in the Device model"""

    def test_get_device_attrs_template_valid_types(self):
        """Test get_device_attrs_template with various valid device types"""
        valid_types = ["Whiskers Node V1", "Whiskers Gateway V1"]

        for device_type in valid_types:
            with self.subTest(device_type=device_type):
                attrs = get_device_attrs_template(device_type)
                self.assertIsInstance(attrs, dict)
                self.assertIn("client", attrs)

    def test_get_device_attrs_template_invalid_type(self):
        """Test get_device_attrs_template with invalid device type"""
        attrs = get_device_attrs_template("Invalid Type")
        self.assertEqual(attrs, {})

    def test_get_device_attrs_template_empty_type(self):
        """Test get_device_attrs_template with empty string"""
        attrs = get_device_attrs_template("")
        self.assertEqual(attrs, {})

    def test_get_device_attrs_template_none_type(self):
        """Test get_device_attrs_template with None"""
        attrs = get_device_attrs_template(None)
        self.assertEqual(attrs, {})

    def test_get_device_attrs_template_case_sensitivity(self):
        """Test get_device_attrs_template case sensitivity"""
        # Test that case matters
        attrs_lower = get_device_attrs_template("whiskers node v1")
        attrs_upper = get_device_attrs_template("WHISKERS NODE V1")
        attrs_correct = get_device_attrs_template("Whiskers Node V1")

        # Only the correct case should return valid attributes
        self.assertEqual(attrs_lower, {})
        self.assertEqual(attrs_upper, {})
        self.assertNotEqual(attrs_correct, {})

    def test_get_location_template_default_values(self):
        """Test get_location_template returns correct default values"""
        location = get_location_template()
        self.assertEqual(location["lati"], 0)
        self.assertEqual(location["long"], 0)
        self.assertEqual(location["alti"], 0)
        self.assertEqual(location["oofi"], False)
        self.assertEqual(location["plac"], "Outdoor")

    def test_get_location_template_structure(self):
        """Test get_location_template returns correct structure"""
        location = get_location_template()
        expected_keys = ["lati", "long", "alti", "oofi", "plac"]

        # Check all expected keys are present
        for key in expected_keys:
            self.assertIn(key, location)

        # Check no extra keys
        self.assertEqual(set(location.keys()), set(expected_keys))

    def test_get_location_template_data_types(self):
        """Test get_location_template returns correct data types"""
        location = get_location_template()

        # Check data types
        self.assertIsInstance(location["lati"], (int, float))
        self.assertIsInstance(location["long"], (int, float))
        self.assertIsInstance(location["alti"], (int, float))
        self.assertIsInstance(location["oofi"], bool)
        self.assertIsInstance(location["plac"], str)

    def test_get_location_template_immutability(self):
        """Test that get_location_template returns a new dict each time"""
        location1 = get_location_template()
        location2 = get_location_template()

        # Modify one and ensure the other is not affected
        location1["lati"] = 100
        self.assertEqual(location2["lati"], 0)

        # Ensure they are different objects
        self.assertIsNot(location1, location2)

    def test_get_device_attrs_template_structure(self):
        """Test get_device_attrs_template returns correct structure for valid types"""
        attrs = get_device_attrs_template("Whiskers Node V1")

        if attrs:  # Only test if we get a valid response
            self.assertIsInstance(attrs, dict)
            self.assertIn("client", attrs)
            self.assertIsInstance(attrs["client"], dict)

    def test_utility_functions_consistency(self):
        """Test that utility functions return consistent results"""
        # Test multiple calls return same results
        attrs1 = get_device_attrs_template("Whiskers Node V1")
        attrs2 = get_device_attrs_template("Whiskers Node V1")
        self.assertEqual(attrs1, attrs2)

        location1 = get_location_template()
        location2 = get_location_template()
        self.assertEqual(location1, location2)
