from django.test import TestCase, tag
from unittest.mock import patch, MagicMock
import json

from device_manager.models import Device
from device_manager.utils.device_cache import DeviceCache
from fields.models import Field


class BaseDeviceCacheTestCase(TestCase):
    """Base test case with common setup for device cache tests"""

    def setUp(self):
        """Set up test data"""
        # Create a test field
        self.field = Field.objects.create(
            name="Test Field",
            cord={"type": "Polygon", "coordinates": [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]},
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create test devices
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25, "Motion event.": False, "Shock event.": False, "Motionless event.": True}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )

        self.device2 = Device.objects.create(
            name="Test Device 2",
            desc="Second Test Device",
            euid="FEDCBA9876543210",
            stat="Warning",
            temp=30,
            batt=60,
            chrg=True,
            actv=False,
            mntc=True,
            hidn=True,
            attr={"client": {"temp": 30, "Motion event.": True, "Shock event.": False, "Motionless event.": False}},
            type="Whiskers Node V2",
            aset="Solar",
            loca={"lati": 1.0, "long": 1.0, "alti": 100.0, "oofi": True, "plac": "Indoor"},
            fild=self.field,
            offp=20
        )

        self.device_cache = DeviceCache()


@tag('unit')
class DeviceCacheUnitTestCase(BaseDeviceCacheTestCase):
    """Unit tests for device cache utility functions with mocked Redis"""

    def setUp(self):
        super().setUp()
        patcher = patch('device_manager.utils.device_cache.get_redis_connection')
        self.mock_get_redis = patcher.start()
        self.addCleanup(patcher.stop)

        # Create mock Redis connection
        self.mock_redis = MagicMock()
        self.mock_get_redis.return_value = self.mock_redis

        # Re-initialize DeviceCache so it picks up the mocked Redis
        self.device_cache = DeviceCache()

    def test_set_device_stat_basic(self):
        """Test the set_device_stat function with basic parameters"""
        self.device_cache.set_device_stat(self.device.id, "Offline", ttl=60)
        self.mock_redis.set.assert_called_once_with(
            f"device:{self.device.id}:stat", "Offline", ex=60
        )

    def test_set_device_stat_different_statuses(self):
        """Test set_device_stat with different status values"""
        statuses = ["Online", "Offline", "Warning", "Error"]
        for status in statuses:
            with self.subTest(status=status):
                self.mock_redis.reset_mock()
                self.device_cache.set_device_stat(self.device.id, status, ttl=120)
                self.mock_redis.set.assert_called_once_with(
                    f"device:{self.device.id}:stat", status, ex=120
                )

    def test_get_device_stat_existing(self):
        """Test the get_device_stat function for existing status"""
        self.mock_redis.get.return_value = b"Offline"
        stat = self.device_cache.get_device_stat(self.device.id)
        self.assertEqual(stat, "Offline")
        self.mock_redis.get.assert_called_once_with(f"device:{self.device.id}:stat")

    def test_get_device_stat_nonexistent(self):
        """Test the get_device_stat function for non-existent status"""
        self.mock_redis.get.return_value = None
        stat = self.device_cache.get_device_stat(self.device.id)
        self.assertIsNone(stat)
        self.mock_redis.get.assert_called_once_with(f"device:{self.device.id}:stat")

    def test_cache_device_basic(self):
        """Test the cache_device function with basic device"""
        self.device_cache.cache_device(self.device, ttl=60)
        # Should be called twice: once for device, once for status
        self.assertEqual(self.mock_redis.set.call_count, 2)

    def test_cache_device_multiple_devices(self):
        """Test caching multiple devices"""
        self.device_cache.cache_device(self.device, ttl=60)
        self.device_cache.cache_device(self.device2, ttl=60)
        self.assertEqual(self.mock_redis.set.call_count, 4)

    def test_get_cached_device_existing(self):
        """Test the get_cached_device function for existing device"""
        self.mock_redis.get.return_value = self.device.to_json()
        cached_device = self.device_cache.get_cached_device(self.device.id)
        self.assertIsNotNone(cached_device)
        self.assertEqual(cached_device.name, self.device.name)
        self.assertEqual(cached_device.euid, self.device.euid)
        self.assertEqual(cached_device.stat, self.device.stat)

    def test_get_cached_device_nonexistent(self):
        """Test the get_cached_device function for non-existent device"""
        self.mock_redis.get.return_value = None
        cached_device = self.device_cache.get_cached_device(self.device.id)
        self.assertIsNone(cached_device)

    def test_get_cached_device_invalid_json(self):
        """Test get_cached_device with invalid JSON data"""
        self.mock_redis.get.return_value = "invalid json data"
        cached_device = self.device_cache.get_cached_device(self.device.id)
        self.assertIsNone(cached_device)



@tag('integration')
class DeviceCacheIntegrationTestCase(BaseDeviceCacheTestCase):
    """Integration tests for device cache utility functions with real Redis"""

    def setUp(self):
        """Set up test data with cache integration"""
        super().setUp()

        # Try to connect to Redis for integration tests
        try:
            from django_redis import get_redis_connection
            self.redis_client = get_redis_connection("default")
            self.redis_client.ping()
            self.redis_available = True

            # Clear any existing test data
            self.redis_client.delete(f"device:{self.device.id}")
            self.redis_client.delete(f"device:{self.device.id}:stat")
            self.redis_client.delete(f"device:{self.device2.id}")
            self.redis_client.delete(f"device:{self.device2.id}:stat")
        except:
            # Skip Redis tests if not available
            self.redis_available = False
            self.redis_client = MagicMock()
            self.redis_client.get.return_value = None
            self.redis_client.set.return_value = True
            self.redis_client.delete.return_value = True
            self.redis_client.ttl.return_value = 60

    def tearDown(self):
        """Clean up after tests"""
        if self.redis_available:
            try:
                self.redis_client.delete(f"device:{self.device.id}")
                self.redis_client.delete(f"device:{self.device.id}:stat")
                self.redis_client.delete(f"device:{self.device2.id}")
                self.redis_client.delete(f"device:{self.device2.id}:stat")
            except:
                pass

    def test_set_device_stat_integration(self):
        """Test the set_device_stat function with real Redis integration"""
        if not self.redis_available:
            self.skipTest("Redis not available for integration test")

        # Set the device status
        self.device_cache.set_device_stat(self.device.id, "Offline", ttl=60)

        # Verify the status was set in Redis
        stat = self.redis_client.get(f"device:{self.device.id}:stat")
        # Handle both string and bytes response
        if isinstance(stat, bytes):
            stat = stat.decode('utf-8')
        self.assertEqual(stat, "Offline")

        # Verify TTL is set
        ttl = self.redis_client.ttl(f"device:{self.device.id}:stat")
        if ttl != -1:
            self.assertLessEqual(ttl, 60)
            self.assertGreater(ttl, 0)

    def test_get_device_stat_integration(self):
        """Test the get_device_stat function with real Redis integration"""
        if not self.redis_available:
            self.skipTest("Redis not available for integration test")

        # Set a status directly in Redis
        self.redis_client.set(f"device:{self.device.id}:stat", "Warning", ex=60)

        # Get the status using the function
        stat = self.device_cache.get_device_stat(self.device.id)
        # Handle both string and bytes response
        if isinstance(stat, bytes):
            stat = stat.decode('utf-8')
        self.assertEqual(stat, "Warning")

    def test_cache_device_integration(self):
        """Test the cache_device function with real Redis integration"""
        if not self.redis_available:
            self.skipTest("Redis not available for integration test")

        # Cache the device with explicit TTL
        self.device_cache.cache_device(self.device, ttl=60)

        # Verify the device was cached in Redis
        cached_json = self.redis_client.get(f"device:{self.device.id}")
        self.assertIsNotNone(cached_json)

        # Parse the JSON and verify the data
        cached_data = json.loads(cached_json)
        self.assertEqual(cached_data["name"], "Test Device")
        self.assertEqual(cached_data["euid"], "ABCDEF0123456789")

        # Verify TTL is set for the device key
        device_ttl = self.redis_client.ttl(f"device:{self.device.id}")
        if device_ttl != -1:
            self.assertLessEqual(device_ttl, 60)
            self.assertGreater(device_ttl, 0)

    def test_get_cached_device_integration(self):
        """Test the get_cached_device function with real Redis integration"""
        if not self.redis_available:
            self.skipTest("Redis not available for integration test")

        # Cache the device directly in Redis
        device_json = self.device.to_json()
        self.redis_client.set(f"device:{self.device.id}", device_json, ex=60)

        # Get the cached device using the function
        cached_device = self.device_cache.get_cached_device(self.device.id)

        # Verify the cached device
        self.assertIsNotNone(cached_device)
        self.assertEqual(cached_device.name, "Test Device")
        self.assertEqual(cached_device.euid, "ABCDEF0123456789")

    def test_cache_operations_workflow_integration(self):
        """Test complete cache workflow with real Redis"""
        if not self.redis_available:
            self.skipTest("Redis not available for integration test")

        # Test complete workflow: cache device, update status, retrieve

        # 1. Cache the device
        self.device_cache.cache_device(self.device, ttl=120)

        # 2. Set device status
        self.device_cache.set_device_stat(self.device.id, "Warning", ttl=120)

        # 3. Retrieve cached device
        cached_device = self.device_cache.get_cached_device(self.device.id)
        self.assertIsNotNone(cached_device)
        self.assertEqual(cached_device.name, self.device.name)

        # 4. Retrieve device status
        cached_stat = self.device_cache.get_device_stat(self.device.id)
        if isinstance(cached_stat, bytes):
            cached_stat = cached_stat.decode('utf-8')
        self.assertEqual(cached_stat, "Warning")

    def test_multiple_devices_cache_integration(self):
        """Test caching multiple devices with real Redis"""
        if not self.redis_available:
            self.skipTest("Redis not available for integration test")

        # Cache both devices
        self.device_cache.cache_device(self.device, ttl=60)
        self.device_cache.cache_device(self.device2, ttl=60)

        # Set different statuses
        self.device_cache.set_device_stat(self.device.id, "Online", ttl=60)
        self.device_cache.set_device_stat(self.device2.id, "Offline", ttl=60)

        # Retrieve both devices
        cached_device1 = self.device_cache.get_cached_device(self.device.id)
        cached_device2 = self.device_cache.get_cached_device(self.device2.id)

        # Verify both devices are correctly cached
        self.assertIsNotNone(cached_device1)
        self.assertIsNotNone(cached_device2)
        self.assertEqual(cached_device1.name, self.device.name)
        self.assertEqual(cached_device2.name, self.device2.name)

        # Verify statuses
        stat1 = self.device_cache.get_device_stat(self.device.id)
        stat2 = self.device_cache.get_device_stat(self.device2.id)

        if isinstance(stat1, bytes):
            stat1 = stat1.decode('utf-8')
        if isinstance(stat2, bytes):
            stat2 = stat2.decode('utf-8')

        self.assertEqual(stat1, "Online")
        self.assertEqual(stat2, "Offline")
