from django.test import TestCase, tag
from django.core.exceptions import ValidationError
from unittest.mock import patch

from device_manager.forms import DeviceCreateUpdateForm
from device_manager.models import Device
from fields.models import Field


class BaseFormTestCase(TestCase):
    """Base test case with common setup for form tests"""

    def setUp(self):
        """Set up test data"""
        # Create test field
        self.field = Field.objects.create(
            name="Test Field",
            cord={"type": "Polygon", "coordinates": [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]},
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Valid form data
        self.valid_form_data = {
            'name': 'Test Device',
            'desc': 'Test Description',
            'euid': 'AB  •  CD  •  EF  •  01  •  23  •  45  •  67  •  89',
            'type': 'Whiskers Node V1',
            'aset': 'Battery',
            'lati': 12.345,
            'long': 67.890,
            'alti': 100.0,
            'plac': 'Outdoor',
            'fild': str(self.field.id),
            'offp': 60
        }


@tag('unit')
class DeviceCreateUpdateFormUnitTestCase(BaseFormTestCase):
    """Unit tests for DeviceCreateUpdateForm"""

    def test_form_initialization_without_instance(self):
        """Test form initialization without instance"""
        form = DeviceCreateUpdateForm()
        
        # Check that field choices are populated
        field_choices = form.fields['fild'].choices
        self.assertIn((self.field.id, self.field.name), field_choices)

    def test_form_initialization_with_instance(self):
        """Test form initialization with existing device instance"""
        device = Device.objects.create(
            name="Existing Device",
            desc="Existing Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 12.345, "long": 67.890, "alti": 100.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=60
        )
        
        form = DeviceCreateUpdateForm(instance=device)
        
        # Check that location fields are populated from instance
        self.assertEqual(form.fields['lati'].initial, 12.345)
        self.assertEqual(form.fields['long'].initial, 67.890)
        self.assertEqual(form.fields['alti'].initial, 100.0)
        self.assertEqual(form.fields['plac'].initial, "Outdoor")

    def test_form_initialization_with_instance_missing_location_data(self):
        """Test form initialization with instance missing location data"""
        device = Device.objects.create(
            name="Device No Location",
            desc="Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={},  # Empty location data
            fild=self.field,
            offp=60
        )
        
        form = DeviceCreateUpdateForm(instance=device)
        
        # Check default values are used when location data is missing
        self.assertEqual(form.fields['lati'].initial, 0.0)
        self.assertEqual(form.fields['long'].initial, 0.0)
        self.assertEqual(form.fields['alti'].initial, 0.0)
        self.assertEqual(form.fields['plac'].initial, "Other")

    def test_valid_form_data(self):
        """Test form with valid data"""
        form = DeviceCreateUpdateForm(data=self.valid_form_data)
        
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")

    def test_form_clean_method(self):
        """Test form clean method processes data correctly"""
        form = DeviceCreateUpdateForm(data=self.valid_form_data)
        
        self.assertTrue(form.is_valid())
        cleaned_data = form.cleaned_data
        
        # Check that location data is properly structured
        expected_loca = {
            'lati': 12.345,
            'long': 67.890,
            'alti': 100.0,
            'plac': 'Outdoor',
            'oofi': False
        }
        self.assertEqual(cleaned_data['loca'], expected_loca)
        
        # Check that EUI is cleaned (uppercase, no separators)
        self.assertEqual(cleaned_data['euid'], 'ABCDEF0123456789')

    def test_form_clean_euid_formatting(self):
        """Test EUI formatting in clean method"""
        test_cases = [
            ('ab  •  cd  •  ef  •  01  •  23  •  45  •  67  •  89', 'ABCDEF0123456789'),
            ('AB  •  CD  •  EF  •  01  •  23  •  45  •  67  •  89', 'ABCDEF0123456789'),
            ('12  •  34  •  56  •  78  •  9A  •  BC  •  DE  •  F0', '123456789ABCDEF0'),
        ]
        
        for input_euid, expected_euid in test_cases:
            with self.subTest(input_euid=input_euid):
                form_data = self.valid_form_data.copy()
                form_data['euid'] = input_euid
                form = DeviceCreateUpdateForm(data=form_data)
                
                self.assertTrue(form.is_valid())
                self.assertEqual(form.cleaned_data['euid'], expected_euid)

    def test_form_field_widgets_and_attributes(self):
        """Test form field widgets and attributes"""
        form = DeviceCreateUpdateForm()
        
        # Test name field
        name_widget = form.fields['name'].widget
        self.assertEqual(name_widget.attrs['class'], 'form-control')
        self.assertEqual(name_widget.attrs['maxlength'], 25)
        
        # Test description field
        desc_widget = form.fields['desc'].widget
        self.assertEqual(desc_widget.attrs['class'], 'form-control')
        self.assertEqual(desc_widget.attrs['rows'], 5)
        self.assertEqual(desc_widget.attrs['maxlength'], 2048)
        
        # Test EUI field
        euid_widget = form.fields['euid'].widget
        self.assertEqual(euid_widget.attrs['class'], 'form-control')
        self.assertEqual(euid_widget.attrs['maxlength'], 51)
        self.assertIn('data-mask-format', euid_widget.attrs)

    def test_form_choices(self):
        """Test form choice fields have correct options"""
        form = DeviceCreateUpdateForm()
        
        # Test type choices
        type_choices = [choice[0] for choice in form.fields['type'].choices]
        self.assertIn('Whiskers Node V1', type_choices)
        self.assertIn('Whiskers Gateway V1', type_choices)
        
        # Test asset choices
        asset_choices = [choice[0] for choice in form.fields['aset'].choices]
        expected_assets = ['Battery', 'Box', 'Cable', 'Drum', 'Person', 'Solar Panel', 'Station', 'Vehicle', 'Other']
        for asset in expected_assets:
            self.assertIn(asset, asset_choices)
        
        # Test placement choices
        placement_choices = [choice[0] for choice in form.fields['plac'].choices]
        expected_placements = ['Indoor', 'Outdoor', 'Other']
        for placement in expected_placements:
            self.assertIn(placement, placement_choices)

    def test_required_fields(self):
        """Test required field validation"""
        required_fields = ['name', 'euid', 'type', 'aset', 'lati', 'long', 'alti', 'plac', 'fild', 'offp']
        
        for field_name in required_fields:
            with self.subTest(field=field_name):
                form_data = self.valid_form_data.copy()
                del form_data[field_name]
                form = DeviceCreateUpdateForm(data=form_data)
                
                self.assertFalse(form.is_valid())
                self.assertIn(field_name, form.errors)

    def test_optional_fields(self):
        """Test optional field validation"""
        # Description is optional
        form_data = self.valid_form_data.copy()
        del form_data['desc']
        form = DeviceCreateUpdateForm(data=form_data)
        
        self.assertTrue(form.is_valid())

    def test_clean_fild_valid_field(self):
        """Test clean_fild method with valid field ID"""
        form = DeviceCreateUpdateForm(data=self.valid_form_data)
        
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['fild'], self.field)

    def test_clean_fild_invalid_field(self):
        """Test clean_fild method with invalid field ID"""
        form_data = self.valid_form_data.copy()
        form_data['fild'] = '99999'  # Non-existent field ID
        form = DeviceCreateUpdateForm(data=form_data)
        
        self.assertFalse(form.is_valid())
        self.assertIn('fild', form.errors)
        # The actual error message from Django's ChoiceField validation
        self.assertIn('Select a valid choice', str(form.errors['fild']))

    def test_form_field_labels(self):
        """Test form field labels"""
        form = DeviceCreateUpdateForm()
        
        expected_labels = {
            'name': 'Name',
            'desc': 'Description',
            'euid': 'EUI-64',
            'type': 'Type',
            'aset': 'Asset',
            'lati': 'Latitude',
            'long': 'Longitude',
            'alti': 'Altitude',
            'plac': 'Placement',
            'fild': 'Assigned Field',
            'offp': 'Offline Period'
        }
        
        for field_name, expected_label in expected_labels.items():
            self.assertEqual(form.fields[field_name].label, expected_label)

    def test_form_field_initial_values(self):
        """Test form field initial values"""
        form = DeviceCreateUpdateForm()
        
        # Test numeric fields have correct initial values
        self.assertEqual(form.fields['lati'].initial, 0.0)
        self.assertEqual(form.fields['long'].initial, 0.0)
        self.assertEqual(form.fields['alti'].initial, 0.0)

    def test_form_meta_configuration(self):
        """Test form Meta configuration"""
        form = DeviceCreateUpdateForm()
        
        # Check model
        self.assertEqual(form._meta.model, Device)
        
        # Check fields
        expected_fields = [
            'name', 'desc', 'type', 'aset', 'loca', 'euid', 'plac', 'fild', 'offp'
        ]
        self.assertEqual(form._meta.fields, expected_fields)

    def test_form_save_creates_device(self):
        """Test form save method creates device correctly"""
        form = DeviceCreateUpdateForm(data=self.valid_form_data)
        
        self.assertTrue(form.is_valid())
        
        # Mock the save to avoid database issues in tests
        with patch.object(form, 'save') as mock_save:
            mock_device = Device(
                name=form.cleaned_data['name'],
                euid=form.cleaned_data['euid'],
                type=form.cleaned_data['type']
            )
            mock_save.return_value = mock_device
            
            device = form.save()
            mock_save.assert_called_once()
            self.assertEqual(device.name, 'Test Device')

    def test_form_validation_edge_cases(self):
        """Test form validation with edge cases"""
        # Test with minimum valid data
        minimal_data = {
            'name': 'A',  # Minimum length
            'euid': '00  •  00  •  00  •  00  •  00  •  00  •  00  •  00',
            'type': 'Whiskers Node V1',
            'aset': 'Other',
            'lati': -90.0,  # Minimum latitude
            'long': -180.0,  # Minimum longitude
            'alti': -1000.0,  # Negative altitude
            'plac': 'Other',
            'fild': str(self.field.id),
            'offp': 1  # Minimum offline period
        }
        
        form = DeviceCreateUpdateForm(data=minimal_data)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")

    def test_form_field_max_lengths(self):
        """Test form field maximum length validation"""
        # Test name max length
        long_name = 'A' * 26  # Exceeds maxlength of 25
        form_data = self.valid_form_data.copy()
        form_data['name'] = long_name
        form = DeviceCreateUpdateForm(data=form_data)
        
        # Note: This test depends on client-side validation
        # The form might still be valid server-side
        # but the widget has maxlength attribute for client-side validation

    def test_duplicate_init_methods(self):
        """Test that the duplicate __init__ method doesn't cause issues"""
        # The form has two __init__ methods, test that it still works
        form = DeviceCreateUpdateForm()
        
        # Should still have field choices populated
        field_choices = form.fields['fild'].choices
        self.assertIn((self.field.id, self.field.name), field_choices)


@tag('integration')
class DeviceFormIntegrationTestCase(BaseFormTestCase):
    """Integration tests for device forms"""

    def test_form_integration_with_database(self):
        """Test form integration with actual database operations"""
        form = DeviceCreateUpdateForm(data=self.valid_form_data)
        
        self.assertTrue(form.is_valid())
        
        # Test that we can create a device using the form
        device = form.save(commit=False)
        device.stat = "Online"
        device.temp = 25
        device.batt = 80
        device.chrg = False
        device.actv = True
        device.mntc = False
        device.hidn = False
        device.attr = {"client": {"temp": 25}}
        device.save()
        
        # Verify device was created correctly
        self.assertEqual(device.name, 'Test Device')
        self.assertEqual(device.euid, 'ABCDEF0123456789')
        self.assertEqual(device.loca['lati'], 12.345)
        self.assertEqual(device.fild, self.field)

    def test_form_update_existing_device(self):
        """Test form can update existing device"""
        # Create initial device
        device = Device.objects.create(
            name="Original Device",
            desc="Original Description",
            euid="ORIG123456789ABC",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Indoor"},
            fild=self.field,
            offp=30
        )
        
        # Update with form
        update_data = self.valid_form_data.copy()
        update_data['name'] = 'Updated Device'
        form = DeviceCreateUpdateForm(data=update_data, instance=device)
        
        self.assertTrue(form.is_valid())
        updated_device = form.save()
        
        # Verify updates
        self.assertEqual(updated_device.name, 'Updated Device')
        self.assertEqual(updated_device.euid, 'ABCDEF0123456789')
        self.assertEqual(updated_device.id, device.id)  # Same device
