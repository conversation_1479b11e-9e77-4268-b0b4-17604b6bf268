from django.urls import path

from .views import (
    DeviceListView,
    DeviceCreateView,
    DeviceDetailView,
    DeviceUpdateView,
    delete,
    generate_address,
    toggle_hide,
    toggle_maintenance,
    clear_status,
    clear_all_status,
    get_user_devices_for_field,
)

app_name = "device_manager"

urlpatterns = [
    path("list/", DeviceListView.as_view(), name="list"),
    path("create/", DeviceCreateView.as_view(), name="create"),
    path("<int:device_id>/", DeviceDetailView.as_view(), name="detail"),
    path("edit/<int:device_id>/", DeviceUpdateView.as_view(), name="edit"),
    path("delete/<int:device_id>/", delete, name="delete"),
    path("toggle_hide/<int:device_id>/", toggle_hide, name="toggle_hide"),
    path(
        "toggle_maintenance/<int:device_id>/",
        toggle_maintenance,
        name="toggle_maintenance",
    ),
    path("generate_address/", generate_address, name="generate_address"),
    path("clear_status/<int:device_id>/", clear_status, name="clear_status"),
    path("clear_all_status/", clear_all_status, name="clear_all_status"),
    path("get_user_devices/<int:field_id>/", get_user_devices_for_field, name="get_user_devices_for_field"),
]
