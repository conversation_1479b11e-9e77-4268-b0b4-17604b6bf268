# Generated by Django 4.2.16 on 2025-05-26 05:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('device_manager', '0010_remove_device_cfgs'),
    ]

    operations = [
        migrations.AddField(
            model_name='device',
            name='direction',
            field=models.FloatField(default=0.0, help_text='Direction of movement in degrees (0-360)'),
        ),
        migrations.AddField(
            model_name='device',
            name='speed',
            field=models.FloatField(default=0.0, help_text='Average speed of the device in km/h'),
        ),
    ]
