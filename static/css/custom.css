#asset,
#battery,
#temperature,
#latitude,
#field_id,
#longitude {
    display: none;
}

.clickable {
    cursor: pointer;
    transition: background-color 0.2s ease;
    padding: 3px;
    padding-top: 7px;
    padding-bottom: 7px;
    border-radius: 3px;
}

.errorlist {
    list-style: none;
    color: #fa6767;
    padding-left: 0;
    margin-top: 0;
    margin-bottom: 0;
}

.errorlist li {
    margin-bottom: 5px;
    /* Add some spacing between error messages */
}

.custom-progress {
    background-color: #c5d3e2
}

.clickable:hover {
    background-color: #f6f6f6;
}

#centered-td-number {
    text-align: center;
}

.font-32 {
    font-size: 32px !important;
}

.dt-scrollable-cell {
    overflow-x: scroll;
    white-space: nowrap;
    height: 100%;
    /* set height to ensure scrollbar appears */
}

.checkbox-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

body {
    background-color: black;
    font-family: cursive;
}

.glow-danger {
    animation: glow-danger 0.5s ease-in-out infinite alternate;
}

@keyframes glow-danger {
    from {
        text-shadow: 0 0 10px #ff8f8f, 0 0 20px #ff8f8f, 0 0 30px #fc6767, 0 0 40px #fc6767, 0 0 50px #fd6f6f, 0 0 60px #fd9999, 0 0 70px #f79191;
    }

    to {
        text-shadow: 0 0 20px #ff8f8f, 0 0 30px #ff8f8f, 0 0 40px #ffffff, 0 0 50px #ffffff, 0 0 60px #ffffff, 0 0 70px #ffffff, 0 0 80px #ffffff;
    }
}

.glow-warning {
    animation: glow-warning 0.5s ease-in-out infinite alternate;
}

@keyframes glow-warning {
    from {
        text-shadow: 0 0 10px #ffdc8f, 0 0 20px #ffdc8f, 0 0 30px #fcd467, 0 0 40px #fcd467, 0 0 50px #fddc6f, 0 0 60px #fde299, 0 0 70px #f7ce91;
    }

    to {
        text-shadow: 0 0 20px #ffdc8f, 0 0 30px #ffdc8f, 0 0 40px #ffffff, 0 0 50px #ffffff, 0 0 60px #ffffff, 0 0 70px #ffffff, 0 0 80px #ffffff;
    }
}
#logo {
    max-width: 90% !important;
}
.filter-green{
    filter: invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg) brightness(118%) contrast(119%);
}
.filter-white{
    filter: brightness(1.3);
}
.filter-gray{
    filter: invert(82%) sepia(5%) saturate(174%) hue-rotate(174deg) brightness(88%) contrast(89%);
}

/* Custom styles for the floating controls panel */
#map-controls-panel {
    max-height: 80vh;
    overflow-y: auto;
    width: 300px;
    right: -300px;
    transition: right 0.3s ease;
    top: 50%;
    transform: translateY(-50%);
    background: white;
    border-radius: 8px 0 0 8px;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

#map-controls-panel .card {
    border: none;
    box-shadow: none;
}

#controls-toggle-btn {
    transition: all 0.3s ease;
    right: 20px;
}

#controls-toggle-btn:hover {
    transform: translateY(-50%) scale(1.1);
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
    #map-controls-panel {
      width: 280px;
      right: -280px;
    }

    #controls-toggle-btn {
      width: 45px !important;
      height: 45px !important;
      right: 15px !important;
    }

    /* Ensure panel doesn't show partially on mobile */
    body {
      overflow-x: hidden;
    }

    /* Adjust container to prevent horizontal scroll */
    .container-fluid {
      padding-left: 15px;
      padding-right: 15px;
    }
}

  /* Extra small devices (phones, less than 576px) */
@media (max-width: 575.98px) {
    #map-controls-panel {
        width: 260px;
        right: -260px;
}

#controls-toggle-btn {
    width: 40px !important;
    height: 40px !important;
    right: 10px !important;
  }
}

  /* Smooth scrollbar for the panel */
#map-controls-panel::-webkit-scrollbar {
    width: 6px;
}

#map-controls-panel::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

 #map-controls-panel::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#map-controls-panel::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.center{
    margin: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

#controls-toggle-btn {
    top: 50%;
    transform: translateY(-50%);
    z-index: 1001;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}