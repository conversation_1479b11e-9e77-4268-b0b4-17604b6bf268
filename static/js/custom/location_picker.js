async function getLocation() {
    const position = new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject);
    });
    return position;
}

var fieldChanged = false;

async function initMap() {
    var idLati = parseFloat(document.getElementById('id_lati').value);
    var idLong = parseFloat(document.getElementById('id_long').value);
    var position;

    if (idLati === 0 && idLong === 0) {
        position = await getLocation();
    } else {
        position = {
            coords: {
                latitude: idLati,
                longitude: idLong
            }
        };
    }

    var fieldId = document.getElementById('id_fild').value;
    console.log('Initial field ID:', fieldId);

    // Check if this is a new form or an edit form
    var isEditForm = fieldId && fieldId !== '0' && fieldId !== '';

    // Store references to device markers and info windows
    var deviceMarkers = [];
    var infoWindows = {};

    // Cache for marker icons to improve performance
    var markerIconCache = {};

    // Function to clear all device markers from the map
    function clearDeviceMarkers() {
        deviceMarkers.forEach(marker => marker.setMap(null));
        deviceMarkers = [];

        // Also clear any open info windows
        for (const id in infoWindows) {
            infoWindows[id].close();
            delete infoWindows[id];
        }
    }

    console.log('Loading Google Maps libraries...');
    // Request needed libraries.
    //@ts-ignore
    const { Map } = await google.maps.importLibrary("maps");
    console.log('Maps library loaded');

    // We're not using AdvancedMarkerElement anymore, but we'll keep this for reference
    // const { AdvancedMarkerElement } = await google.maps.importLibrary("marker");

    var map = new Map(document.getElementById('location_picker'), {
        center: { lat: position.coords.latitude, lng: position.coords.longitude },
        zoom: 8,
        mapId: "CREATE"
    });

    var marker = new google.maps.Marker({
        position: { lat: position.coords.latitude, lng: position.coords.longitude },
        map: map,
        draggable: true
    });

    function updateMarkerAndForm(latitude, longitude) {
        marker.setPosition({ lat: latitude, lng: longitude });
        document.getElementById('id_lati').value = parseFloat(latitude.toFixed(6));
        document.getElementById('id_long').value = parseFloat(longitude.toFixed(6));
    }

    updateMarkerAndForm(position.coords.latitude, position.coords.longitude);

    google.maps.event.addListener(marker, 'dragend', function (event) {
        updateMarkerAndForm(event.latLng.lat(), event.latLng.lng());
    });

    google.maps.event.addListener(map, 'click', function (event) {
        updateMarkerAndForm(event.latLng.lat(), event.latLng.lng());
    });

    document.getElementById('id_lati').addEventListener('input', function () {
        var latitude = parseFloat(this.value);
        var longitude = parseFloat(document.getElementById('id_long').value);
        updateMarkerAndForm(latitude, longitude);
        map.setCenter({ lat: latitude, lng: longitude });
    });

    document.getElementById('id_long').addEventListener('input', function () {
        var latitude = parseFloat(document.getElementById('id_lati').value);
        var longitude = parseFloat(this.value);
        updateMarkerAndForm(latitude, longitude);
        map.setCenter({ lat: latitude, lng: longitude });
    });

    // Store references to the polygons
    var polygons = [];

    renderField(fieldId);

    // Listen to field selector value changes
    var fieldSelector = document.getElementById('id_fild');
    fieldSelector.addEventListener('change', function () {
        fieldChanged = true;
        fieldId = this.value;

        // Remove previous polygons from the map
        polygons.forEach(function (polygon) {
            polygon.setMap(null);
        });
        polygons = [];

        // Clear existing device markers
        clearDeviceMarkers();

        if (fieldId) {
            renderField(fieldId);
        }
    });

    function renderField(fieldId) {
        if (!fieldId) return;

        // Clear existing device markers
        clearDeviceMarkers();

        // Make a request to fetch the fields data from the backend
        fetch('/fields/get/' + fieldId)
            .then(response => response.json())
            .then(field => {
                // Render the polygons for the selected field
                var polygon = new google.maps.Polygon({
                    paths: field.cord,
                    strokeColor: field.colr,
                    strokeOpacity: 0.8,
                    strokeWeight: 2,
                    fillColor: field.colr,
                    fillOpacity: 0.35,
                    map: map
                });
                polygons.push(polygon);

                // Move the marker to the center of the polygon
                var bounds = new google.maps.LatLngBounds();
                field.cord.forEach(function (coord) {
                    bounds.extend(coord);
                });
                map.fitBounds(bounds);
                if (fieldChanged)
                    marker.setPosition(bounds.getCenter());

                // Update the input values with the new center coordinates
                var center = bounds.getCenter();
                if (fieldChanged)
                    updateMarkerAndForm(center.lat(), center.lng());

                // Fetch and display user's devices for this field
                fetchAndDisplayUserDevices(fieldId, bounds);
            })
            .catch(error => {
                console.error('Error fetching field:', error);
            });
    }

    // Variables moved to the top of the file

    function fetchAndDisplayUserDevices(fieldId, bounds) {
        console.log('Fetching devices for field ID:', fieldId);

        // Check if fieldId is valid
        if (!fieldId || fieldId === '0' || fieldId === '') {
            console.log('Invalid field ID, skipping device fetch');
            return;
        }

        fetch('/device/get_user_devices/' + fieldId + '/')
            .then(response => response.json())
            .then(devices => {
                console.log(devices.length, ' Devices received');
                // Create markers for each device
                devices.forEach(device => {
                    // Skip devices with invalid coordinates
                    if (!device.latitude || !device.longitude) return;

                    // Create a marker for the device
                    var devicePosition = {
                        lat: device.latitude,
                        lng: device.longitude
                    };

                    // Create a marker icon based on device type and status
                    var deviceMarker = createDeviceMarker(device, devicePosition);

                    // Add to the markers array for later cleanup
                    deviceMarkers.push(deviceMarker);

                    // Extend bounds to include this device
                    bounds.extend(devicePosition);
                });

                // Adjust map to show all devices
                if (deviceMarkers.length > 0) {
                    map.fitBounds(bounds);
                }
            })
            .catch(error => {
                console.error('Error fetching devices:', error);
            });
    }

    function createDeviceMarker(device, position) {
        // Create marker icon based on device type and status
        var icon = getDeviceIcon(device);

        // Create the marker
        var marker = new google.maps.Marker({
            position: position,
            map: map,
            title: device.name,
            icon: icon
        });

        // Create info window content
        var infoWindowContent = `
            <div style="background-color: rgba(0, 0, 0, 0.8); color: white; padding: 10px; border-radius: 5px; font-size: 1.1em;">
                <span style="font-weight: bold;">Name: </span> <span>${device.name}</span><br>
                <span style="font-weight: bold;">Type: </span> <span>${device.type}</span><br>
                <span style="font-weight: bold;">Status: </span> <span>${device.status}</span><br>
                <span style="font-weight: bold;">Location: </span> <span>${device.place}</span><br>
                <span style="font-weight: bold;">Coordinates: </span> <span>${device.latitude.toFixed(6)}, ${device.longitude.toFixed(6)}</span>
            </div>
        `;

        // Create info window
        var infoWindow = new google.maps.InfoWindow({
            content: infoWindowContent
        });

        // Store the info window for later reference
        infoWindows[device.id] = infoWindow;

        // Add click listener to open info window
        marker.addListener('click', function() {
            // Close any open info windows first
            for (const id in infoWindows) {
                infoWindows[id].close();
            }
            infoWindow.open(map, marker);
        });

        return marker;
    }

    function getDeviceIcon(device) {
        // Select marker icon based on device status and type
        let iconName = device.asset.toLowerCase();

        if (device.maintenance) {
            iconName += "-maintenance";
        } else {
            if (device.status === "Online") {
                iconName += "-success";
            } else if (device.status === "Offline") {
                iconName += "-offline";
            } else if (device.status === "Warning") {
                iconName += "-warning";
            } else {
                iconName += "-danger";
            }
        }

        // Create a cache key for this icon
        var iconPath = '/static/images/device/icons/' + iconName + '.png';
        var cacheKey = `icon_${iconPath}`;

        // Check if we have this icon in cache
        if (markerIconCache[cacheKey]) {
            return markerIconCache[cacheKey];
        }

        // If not in cache, create a new icon configuration
        var iconConfig = {
            url: iconPath,
            scaledSize: new google.maps.Size(48, 48),
            origin: new google.maps.Point(0, 0),
            anchor: new google.maps.Point(24, 24)
        };

        // Store in cache for future use
        markerIconCache[cacheKey] = iconConfig;

        return iconConfig;
    }

    // Function removed as it's no longer used

}