$(window).on('load', function () {
    const wsScheme = window.location.protocol === "https:" ? "wss" : "ws";
    const deviceWsPath = `${wsScheme}://${window.location.host}/ws/device/`;

    const deviceDataSocket = new ReconnectingWebSocket(deviceWsPath);
    let lastUpdateTimestamp = null; // To store the last update timestamp

    deviceDataSocket.onmessage = function (event) {
        const data = JSON.parse(event.data);

        // get object from first key in the json
        const deviceId = Object.keys(data)[0];
        const device = data[Object.keys(data)[0]];

        battery = device.batt;
        batteryIndicator.updateSeries([battery]);
        batteryIndicator.updateOptions({
            colors: battery <= 25 ? ['#fa6767'] : battery <= 50 ? ['#f9bc0d'] : ['#42d29d'],
        });

        temperature = device.attr.client["temp"];
        thermometer.updateSeries([temperature]);
        thermometer.updateOptions({
            colors: temperature <= 60 ? ['#3688fc'] : ['#fa6767'],
        });

        // update packets transmitted, received, corrupted, and acknowledgement rate
        document.querySelector('#transmitted').innerHTML = device.attr.client["txok"];
        document.querySelector('#pending').innerHTML = device.attr.client["txin"];
        document.querySelector('#acknowledgement').innerHTML = device.attr.client["ackr"];
        document.querySelector('#received').innerHTML = device.attr.client["rxok"];
        document.querySelector('#forwarded').innerHTML = device.attr.client["rxfw"];

        // Update the last update timestamp and start the counter
        lastUpdateTimestamp = new Date(device.lupd);
        document.querySelector('#last-update').innerHTML = formatTimeElapsed(lastUpdateTimestamp);

        updateMarker(deviceId, device.name, device.loca.lati, device.loca.long, device.aset, device.mntc, device.stat, device.speed || 0, device.direction || 0);
    };

    deviceDataSocket.onclose = function () {
        let connectionElement = document.querySelector('#ws-connection');
        connectionElement.innerHTML = 'DISCONNECTED';
        connectionElement.classList.remove('bg-success');
        connectionElement.classList.add('bg-secondary');
    }

    deviceDataSocket.onopen = function () {
        deviceDataSocket.send(JSON.stringify({ "devices": deviceIds }));

        let connectionElement = document.querySelector('#ws-connection');
        connectionElement.innerHTML = 'CONNECTED';
        connectionElement.classList.add('bg-success');
        connectionElement.classList.remove('bg-secondary');
    }

    // Function to update the time elapsed every second
    setInterval(function () {
        if (lastUpdateTimestamp) {
            document.querySelector('#last-update').innerHTML = formatTimeElapsed(lastUpdateTimestamp);
        }
    }, 1000);
});
