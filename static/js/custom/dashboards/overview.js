$(window).on('load', function () {
    const wsScheme = window.location.protocol === "https:" ? "wss" : "ws";
    const deviceWsPath = `${wsScheme}://${window.location.host}/ws/device/`;

    const debugModeTogglePanel = document.getElementById('debug-mode-toggle-panel');
    if (typeof window.mapDebugMode === 'undefined') {
        // Disable debug mode by default in production
        if (debugModeTogglePanel.checked) {
            window.mapDebugMode = true;
        }else{
            window.mapDebugMode = false;
        }
    }

    // Initialize cluster radius input with default value
    const clusterRadiusInput = document.getElementById('cluster-radius');
    if (clusterRadiusInput && typeof defaultClusterRadius !== 'undefined') {
        clusterRadiusInput.value = defaultClusterRadius;

        // Add input event listener to update the display in real-time as the slider moves
        clusterRadiusInput.addEventListener('input', function() {
            const radiusValue = parseFloat(this.value);
            const radiusDisplay = document.getElementById('radius-value-display');
            if (radiusDisplay) {
                radiusDisplay.textContent = radiusValue.toFixed(1);
            }
        });

        // Log the initial cluster radius for debugging
        if (window.mapDebugMode) {
            console.log(`Initial cluster radius set to ${defaultClusterRadius} km`);
        }
    }

    // Initialize marker visibility zoom threshold
    const markerVisibilityZoom = document.getElementById('marker-visibility-zoom');
    if (markerVisibilityZoom) {
        // Set initial value to match the constant in device_map.js
        if (typeof MARKER_VISIBILITY_ZOOM !== 'undefined') {
            markerVisibilityZoom.value = MARKER_VISIBILITY_ZOOM.toString();
        }
    }

    // Initialize global variables for panel controls
    if (typeof window.ENABLE_AUTO_ZOOM === 'undefined') {
        window.ENABLE_AUTO_ZOOM = false;
    }
    

    const deviceDataSocket = new ReconnectingWebSocket(deviceWsPath);

    let deviceListHtml = new Map();
    let deviceTimestamps = new Map(); // Store last update timestamps

    let noDevicesHtml = `
        <div class="d-flex flex-column justify-content-center align-items-center text-center">
            <i class="mdi mdi-magnify-close" style="font-size: 48px;"></i>
            <p>No devices found.</p>
        </div>
    `

    function updateTimestamps() {
        document.querySelectorAll('.device-last-update').forEach((element) => {
            const deviceId = element.getAttribute('data-id');
            const timestamp = deviceTimestamps.get(parseInt(deviceId));
            if (timestamp) {
                element.innerHTML = formatTimeElapsed(new Date(timestamp));
            }
        });

        // Update #last-update with the latest timestamp of the last device
        const deviceTimestampsArray = Array.from(deviceTimestamps.values());
        const lastDevice = deviceTimestampsArray[deviceTimestampsArray.length - 1];

        if (lastDevice) {
            const lastDeviceTimestamp = new Date(lastDevice);
            document.querySelector('#last-update').innerHTML = formatTimeElapsed(lastDeviceTimestamp);
        }
    }

    function getDeviceBadgeClass(status) {
        if (status === 'Online') {
            return 'badge badge-success-lighten';
        } else if (status === 'Warning') {
            return 'badge badge-warning-lighten';
        } else if (status === 'Offline') {
            return 'badge badge-secondary-lighten';
        } else {
            return 'badge badge-danger-lighten';
        }
    }

    function getBatteryBarColor(deviceBattery) {
        if (deviceBattery <= 25) {
            return "bg-danger";
        } else if (deviceBattery <= 50) {
            return "bg-warning";
        } else if (deviceBattery <= 100) {
            return "bg-success";
        }
    }

    deviceDataSocket.onmessage = function (event) {

        const devices = JSON.parse(event.data);

        // Update device timestamps and HTML
        if (Array.isArray(devices)) {
            devices.forEach((device) => {
                const deviceHtml = `
                <div class="d-flex align-items-start clickable mt-1 p-1 border" onclick="setFocus('${device.id}')">
                  <img class="me-3" src="/static/images/device/icons/${device.aset.toLowerCase().replace(" ", "-")}.png" width="38" alt="Generic placeholder image" />
                  <div class="w-100 overflow-hidden">
                    <span class="badge ${getDeviceBadgeClass(device.stat)} float-end">${device.stat}</span>
                    <h5 class="mt-0 mb-1">${device.name}${device.mntc ? '🚧' : ''}</h5>
                    <div class="row align-items-center">
                      <div class="col-sm-6">
                        <p class="mt-0 mb-0 device-last-update" data-id="${device.id}" style="font-size:12px;">${formatTimeElapsed(new Date(device.lupd))} ago</p>
                      </div>
                      <div class="col-sm-6">
                        <div class="progress progress-sm mt-1" style="background-color: #c5d3e2">
                          <i class="progress-bar progress-lg ${getBatteryBarColor(device.batt)}" style="width: ${device.batt}%"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              `;

                // Update device HTML and timestamp
                if (deviceListHtml.has(device.id)) {
                    deviceListHtml.delete(device.id);
                }

                deviceListHtml.set(device.id, deviceHtml);
                deviceTimestamps.set(device.id, device.lupd);

                updateMarker(device.id, device.name, device.loca.lati, device.loca.long, device.aset, device.mntc, device.stat, device.speed || 0, device.direction || 0);
            });
        }

        // Render updated device list
        document.querySelector('#asset-list-container').innerHTML = deviceListHtml.size === 0 ? noDevicesHtml : Array.from(deviceListHtml.values()).reverse().join('');
    };

    deviceDataSocket.onclose = function () {
        let connectionElement = document.querySelector('#ws-connection');
        connectionElement.innerHTML = 'DISCONNECTED';
        connectionElement.classList.remove('bg-success');
        connectionElement.classList.add('bg-secondary');
    };

    deviceDataSocket.onopen = function () {
        deviceDataSocket.send(JSON.stringify({ "fields": fieldIds }));

        let connectionElement = document.querySelector('#ws-connection');
        connectionElement.innerHTML = 'CONNECTED';
        connectionElement.classList.add('bg-success');
        connectionElement.classList.remove('bg-secondary');
    };

    // Update timestamps every second
    setInterval(updateTimestamps, 1000);

    // Controls panel toggle functionality
    initializeControlsPanel();
});

// Initialize the floating controls panel
function initializeControlsPanel() {
    const controlsPanel = document.getElementById('map-controls-panel');
    const toggleBtn = document.getElementById('controls-toggle-btn');

    if (!controlsPanel || !toggleBtn) {
        if (window.mapDebugMode) {
            console.warn('Controls panel elements not found');
        }
        return;
    }

    const toggleIcon = toggleBtn.querySelector('i');
    let isPanelOpen = false;

    // Get panel width for calculations
    function getPanelWidth() {
        if (window.innerWidth <= 575.98) {
            return 260; // Extra small devices
        } else if (window.innerWidth <= 768) {
            return 280; // Small devices
        } else {
            return 300; // Medium and larger devices
        }
    }

    // Get button right position
    function getButtonRightPosition() {
        if (window.innerWidth <= 575.98) {
            return '10px'; // Extra small devices
        } else if (window.innerWidth <= 768) {
            return '15px'; // Small devices
        } else {
            return '20px'; // Medium and larger devices
        }
    }

    // Toggle panel visibility
    function togglePanel() {
        isPanelOpen = !isPanelOpen;
        const panelWidth = getPanelWidth();
        const buttonOffset = parseInt(getButtonRightPosition());

        if (isPanelOpen) {
            controlsPanel.style.right = '0px';
            toggleBtn.style.right = `${panelWidth + buttonOffset}px`;
            toggleIcon.className = 'mdi mdi-close mdi-18px center';
            toggleBtn.title = 'Close Map Controls';
        } else {
            controlsPanel.style.right = `-${panelWidth}px`;
            toggleBtn.style.right = getButtonRightPosition();
            toggleIcon.className = 'mdi mdi-cog mdi-18px center';
            toggleBtn.title = 'Open Map Controls';
        }
    }

    // Add click event to toggle button
    toggleBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        togglePanel();
    });

    // Initialize panel controls
    function initializePanelControls() {
        // Initialize auto-zoom toggle in panel
        const panelAutoZoom = document.getElementById('auto-zoom-toggle-panel');
        if (panelAutoZoom) {
            // Set the toggle state to match the global variable
            panelAutoZoom.checked = window.ENABLE_AUTO_ZOOM;

            // Add event listener to update the global variable when the toggle changes
            panelAutoZoom.addEventListener('change', function() {
                window.ENABLE_AUTO_ZOOM = this.checked;
                if (window.mapDebugMode) {
                    console.log(`Auto-zoom on updates ${window.ENABLE_AUTO_ZOOM ? 'enabled' : 'disabled'}`);
                }
            });
        }

        // Initialize debug mode toggle in panel
        const panelDebug = document.getElementById('debug-mode-toggle-panel');
        if (panelDebug) {
            // Set the toggle state to match the global variable
            panelDebug.checked = window.mapDebugMode;

            // Add event listener to update the global variable when the toggle changes
            panelDebug.addEventListener('change', function() {
                window.mapDebugMode = this.checked;
                console.log(`Debug mode ${window.mapDebugMode ? 'enabled' : 'disabled'}`);
            });
        }
    }

    // Initialize panel controls after a short delay to ensure all elements are loaded
    setTimeout(initializePanelControls, 100);

    // Close panel when clicking outside
    document.addEventListener('click', function(event) {
        if (isPanelOpen &&
            !controlsPanel.contains(event.target) &&
            !toggleBtn.contains(event.target)) {
            togglePanel();
        }
    });

    // Prevent panel from closing when clicking inside it
    controlsPanel.addEventListener('click', function(event) {
        event.stopPropagation();
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        const panelWidth = getPanelWidth();
        const buttonOffset = parseInt(getButtonRightPosition());

        if (isPanelOpen) {
            controlsPanel.style.right = '0px';
            toggleBtn.style.right = `${panelWidth + buttonOffset}px`;
        } else {
            controlsPanel.style.right = `-${panelWidth}px`;
            toggleBtn.style.right = getButtonRightPosition();
        }

        // Update panel width
        controlsPanel.style.width = `${panelWidth}px`;
    });

    // Add touch event support for mobile
    let touchStartX = 0;
    let touchStartY = 0;

    toggleBtn.addEventListener('touchstart', function(e) {
        touchStartX = e.touches[0].clientX;
        touchStartY = e.touches[0].clientY;
    }, { passive: true });

    toggleBtn.addEventListener('touchend', function(e) {
        const touchEndX = e.changedTouches[0].clientX;
        const touchEndY = e.changedTouches[0].clientY;
        const deltaX = Math.abs(touchEndX - touchStartX);
        const deltaY = Math.abs(touchEndY - touchStartY);

        // Only trigger if it's a tap (not a swipe)
        if (deltaX < 10 && deltaY < 10) {
            e.preventDefault();
            e.stopPropagation();
            togglePanel();
        }
    }, { passive: false });

    // Initialize panel position
    const panelWidth = getPanelWidth();
    controlsPanel.style.right = `-${panelWidth}px`;
    controlsPanel.style.width = `${panelWidth}px`;
    toggleBtn.style.right = getButtonRightPosition();
}
