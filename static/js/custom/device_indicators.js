// Process all devices at once
document.addEventListener('DOMContentLoaded', function() {
    // Process status indicators
    const statusIcons = document.querySelectorAll('[id^="status-icon-"]');
    statusIcons.forEach(icon => {
        const deviceId = icon.id.replace('status-icon-', '');
        const statusText = icon.nextSibling.textContent.trim();
        
        if (statusText === "Online") {
            icon.classList.add("text-success");
        } else if (statusText === "Warning") {
            icon.classList.add("text-warning");
        } else if (statusText === "Danger") {
            icon.classList.add("text-danger");
        }
    });
    
    // Process battery bars
    const batteryBars = document.querySelectorAll('[id^="bg-color-"]');
    batteryBars.forEach(bar => {
        const deviceId = bar.id.replace('bg-color-', '');
        const batteryLevel = parseInt(bar.style.width);
        
        if (batteryLevel <= 25) {
            bar.classList.add("bg-danger");
        } else if (batteryLevel <= 50) {
            bar.classList.add("bg-warning");
        } else if (batteryLevel <= 100) {
            bar.classList.add("bg-success");
        }
    });
});
