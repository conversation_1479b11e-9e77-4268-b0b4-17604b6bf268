// Device Selection Widget JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const deviceSelectionWidget = document.querySelector('.device-selection-widget');
    if (!deviceSelectionWidget) return;

    const deviceAssetFilter = document.getElementById('device-asset-filter');
    const deviceNameFilter = document.getElementById('device-name-filter');
    const fieldFilterButtons = document.querySelectorAll('.field-filter-btn');
    const clearFieldFilterBtn = document.getElementById('clear-field-filter');
    const clearAllFiltersBtn = document.getElementById('clear-all-filters');
    const selectAllBtn = document.getElementById('select-all-devices');
    const deselectAllBtn = document.getElementById('deselect-all-devices');
    const deviceTable = document.getElementById('device-selection-table');
    const deviceRows = deviceTable.querySelectorAll('tbody tr');
    const deviceCheckboxes = document.querySelectorAll('.device-checkbox');

    let activeFieldFilters = new Set();

    // Device type filtering
    deviceAssetFilter.addEventListener('change', function() {
        filterDevices();
    });

    // Device name filtering
    deviceNameFilter.addEventListener('input', function() {
        filterDevices();
    });

    // Field filtering
    fieldFilterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const fieldId = this.getAttribute('data-field-id');
            
            if (this.classList.contains('active')) {
                // Remove filter
                this.classList.remove('active');
                this.classList.remove('btn-primary');
                this.classList.add('btn-outline-primary');
                activeFieldFilters.delete(fieldId);
            } else {
                // Add filter
                this.classList.add('active');
                this.classList.remove('btn-outline-primary');
                this.classList.add('btn-primary');
                activeFieldFilters.add(fieldId);
            }
            
            filterDevices();
        });
    });

    // Clear field filters
    clearFieldFilterBtn.addEventListener('click', function() {
        activeFieldFilters.clear();
        fieldFilterButtons.forEach(button => {
            button.classList.remove('active');
            button.classList.remove('btn-primary');
            button.classList.add('btn-outline-primary');
        });
        filterDevices();
    });

    // Clear all filters
    clearAllFiltersBtn.addEventListener('click', function() {
        // Clear device type filter
        deviceAssetFilter.value = '';

        // Clear device name filter
        deviceNameFilter.value = '';

        // Clear field filters
        activeFieldFilters.clear();
        fieldFilterButtons.forEach(button => {
            button.classList.remove('active');
            button.classList.remove('btn-primary');
            button.classList.add('btn-outline-primary');
        });

        filterDevices();
    });

    // Select all visible devices
    selectAllBtn.addEventListener('click', function() {
        deviceRows.forEach(row => {
            if (row.style.display !== 'none') {
                const checkbox = row.querySelector('.device-checkbox');
                if (checkbox) {
                    checkbox.checked = true;
                }
            }
        });
        updateSaveButtonState();
    });

    // Deselect all visible devices
    deselectAllBtn.addEventListener('click', function() {
        deviceRows.forEach(row => {
            if (row.style.display !== 'none') {
                const checkbox = row.querySelector('.device-checkbox');
                if (checkbox) {
                    checkbox.checked = false;
                }
            }
        });
        updateSaveButtonState();
    });

    // Update save button state when checkboxes change
    deviceCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSaveButtonState);
    });

    function filterDevices() {
        const selectedAsset = deviceAssetFilter.value;
        const nameFilter = deviceNameFilter.value.toLowerCase().trim();

        deviceRows.forEach(row => {
            const deviceAsset = row.getAttribute('data-asset');
            const fieldId = row.getAttribute('data-field-id');
            const deviceName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();

            let showRow = true;

            // Filter by device type
            if (selectedAsset && deviceAsset !== selectedAsset) {
                showRow = false;
            }

            // Filter by device name
            if (nameFilter && !deviceName.includes(nameFilter)) {
                showRow = false;
            }

            // Filter by field
            if (activeFieldFilters.size > 0 && !activeFieldFilters.has(fieldId)) {
                showRow = false;
            }

            row.style.display = showRow ? '' : 'none';
        });

        updateRowCount();
    }

    function updateRowCount() {
        const visibleRows = Array.from(deviceRows).filter(row => row.style.display !== 'none');
        const totalRows = deviceRows.length;
        
        // Update table info if needed
        const tableInfo = document.querySelector('.device-table-info');
        if (tableInfo) {
            tableInfo.textContent = `Showing ${visibleRows.length} of ${totalRows} devices`;
        }
    }

    function updateSaveButtonState() {
        const saveButton = document.getElementById('save-button');
        if (saveButton) {
            // Enable save button if form has changed
            saveButton.disabled = false;
        }
    }

    // Initialize
    filterDevices();
    updateSaveButtonState();
});
