// Global variables
let telemetryChart;
let timelineChart;
let deviceWebSocket;
let currentStartDate;
let currentEndDate;

var telemetryOptions = {
    series: [{
        data: []
    }],
    chart: {
        id: 'chart2',
        type: 'line',
        height: 580,
        toolbar: {
            autoSelected: 'pan',
            show: false
        }
    },
    colors: ['#3688fc'],
    stroke: {
        width: 3
    },
    dataLabels: {
        enabled: false,
    },
    fill: {
        opacity: 1,
    },
    markers: {
        size: 5
    },
    xaxis: {
        type: 'datetime'
    }
};

var timelineOptions = {
    series: [{
        data: []
    }],
    chart: {
        id: 'chart1',
        height: 130,
        type: 'area',
        brush: {
            target: 'chart2',
            enabled: true
        },
        selection: {
            enabled: true,
            xaxis: {
            }
        },
    },
    colors: ['#fa6791'],
    fill: {
        type: 'gradient',
        gradient: {
            opacityFrom: 0.91,
            opacityTo: 0.1,
        }
    },
    xaxis: {
        type: 'datetime',
        tooltip: {
            enabled: false
        }
    },
    yaxis: {
        tickAmount: 2
    }
};

// Utility functions
function formatDateForInput(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

function setDefaultDateRange() {
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));

    currentEndDate = now;
    currentStartDate = sevenDaysAgo;

    document.getElementById('start-date').value = formatDateForInput(sevenDaysAgo);
    document.getElementById('end-date').value = formatDateForInput(now);
}

function fetchTelemetryData(startDate = null, endDate = null) {
    let url = `/telemetry/get-entries/${device_id}/${key}/`;

    if (startDate && endDate) {
        const params = new URLSearchParams({
            start_date: startDate.toISOString(),
            end_date: endDate.toISOString()
        });
        url += `?${params.toString()}`;
    }

    return fetch(url)
        .then(response => response.json())
        .then(data => {
            // Populate the data series with telemetry values and datetime
            const chartData = data.map(item => ({
                x: new Date(item.datetime).getTime(),
                y: parseFloat(item.value)
            }));

            telemetryOptions.series[0].data = chartData;
            timelineOptions.series[0].data = chartData;

            if (data.length > 0) {
                telemetryOptions.series[0].name = data[0].key;
            }

            // Update charts if they exist
            if (telemetryChart) {
                telemetryChart.updateSeries([{
                    data: chartData,
                    name: data.length > 0 ? data[0].key : ''
                }]);
            }

            if (timelineChart) {
                timelineChart.updateSeries([{
                    data: chartData
                }]);
            }

            return data;
        })
        .catch(error => {
            console.error('Error fetching telemetry data:', error);
            throw error;
        });
}

// WebSocket functionality
function initializeWebSocket() {
    const wsScheme = window.location.protocol === "https:" ? "wss" : "ws";
    const deviceWsPath = `${wsScheme}://${window.location.host}/ws/device/${device_id}/`;

    deviceWebSocket = new ReconnectingWebSocket(deviceWsPath);

    deviceWebSocket.onopen = function(event) {
        if (console && console.log) {
            console.log('Telemetry WebSocket connection established');
        }
    };

    deviceWebSocket.onmessage = function(event) {
        try {
            const deviceData = JSON.parse(event.data);
            handleDeviceUpdate(deviceData);
        } catch (error) {
            console.error('Error parsing WebSocket message:', error);
        }
    };

    deviceWebSocket.onerror = function(error) {
        console.error('Telemetry WebSocket error:', error);
    };

    deviceWebSocket.onclose = function(event) {
        if (console && console.log) {
            console.log('Telemetry WebSocket connection closed');
        }
    };
}

function handleDeviceUpdate(deviceData) {
    // Check if the device update contains telemetry data for our key
    if (deviceData && deviceData.attr && deviceData.attr.client && deviceData.attr.client[key] !== undefined) {
        const newValue = parseFloat(deviceData.attr.client[key]);
        const timestamp = new Date().getTime();

        // Add new data point to charts
        if (telemetryChart && timelineChart) {
            const newDataPoint = { x: timestamp, y: newValue };

            // Get current data
            const currentTelemetryData = telemetryChart.w.config.series[0].data;
            const currentTimelineData = timelineChart.w.config.series[0].data;

            // Add new point and keep only data within current date range
            const updatedTelemetryData = [...currentTelemetryData, newDataPoint].filter(point => {
                return point.x >= currentStartDate.getTime() && point.x <= currentEndDate.getTime();
            });

            const updatedTimelineData = [...currentTimelineData, newDataPoint].filter(point => {
                return point.x >= currentStartDate.getTime() && point.x <= currentEndDate.getTime();
            });

            // Update charts
            telemetryChart.updateSeries([{
                data: updatedTelemetryData,
                name: key
            }]);

            timelineChart.updateSeries([{
                data: updatedTimelineData
            }]);
        }
    }
}

// Event handlers
function setupEventHandlers() {
    // Apply filter button
    document.getElementById('apply-filter').addEventListener('click', function() {
        const startDateInput = document.getElementById('start-date').value;
        const endDateInput = document.getElementById('end-date').value;

        if (startDateInput && endDateInput) {
            currentStartDate = new Date(startDateInput);
            currentEndDate = new Date(endDateInput);

            fetchTelemetryData(currentStartDate, currentEndDate);
        }
    });

    // Reset filter button
    document.getElementById('reset-filter').addEventListener('click', function() {
        setDefaultDateRange();
        fetchTelemetryData(currentStartDate, currentEndDate);
    });
}

// Initialize everything
function initializeTelemetryViewer() {
    // Set default date range
    setDefaultDateRange();

    // Setup event handlers
    setupEventHandlers();

    // Initialize WebSocket
    initializeWebSocket();

    // Fetch initial data and create charts
    fetchTelemetryData(currentStartDate, currentEndDate)
        .then(data => {
            // Create charts after data is loaded
            telemetryChart = new ApexCharts(document.querySelector("#telemetry"), telemetryOptions);
            telemetryChart.render();

            timelineChart = new ApexCharts(document.querySelector("#timeline"), timelineOptions);
            timelineChart.render();
        })
        .catch(error => {
            console.error('Error initializing telemetry viewer:', error);
        });
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeTelemetryViewer();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (deviceWebSocket) {
        deviceWebSocket.close();
    }
});
