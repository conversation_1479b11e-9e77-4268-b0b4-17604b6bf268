#!/bin/bash

# Ensure directories exist with correct permissions
echo "Setting up directories..."
mkdir -p /home/<USER>/staticfiles /home/<USER>/media

# Run migrations
echo "Running migrations..."
python manage.py migrate

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput

# Running device status handler
python manage.py preload_devices
python manage.py listen_to_expired_keys &

# Execute the command passed to docker
echo "Starting application..."
exec "$@"