# WhiskersHub
A web application used to provision, manage, and view devices and their telemetry data in a Whiskers network. For a detailed description of WhiskersHub, its applications, and workings, refer to the [Technical Architecture Documenbt](https://esbaaroman.sharepoint.com/:u:/s/WhiskersIoT/ETa6X7tpBhBCi7sl_IPenmABmd3lWoVD1HrvD10ybuRFww?e=WC1jvl) on Microsoft Loop. This README.md is targeted for development specifically.

# Tested Systems
- WSL2
- Ubuntu 22.04

# Docker Guidance
The system works with normal docker-compose commands. Make commands are also available:

1. `make up`: For running the system with all services (For production purposes, CSRF excemptions won't work here).
2. `make down`: To stop the system.
3. `make up-dev`: For running the system without nginx service (For development purposes).
4. `make migrations`: Creates a migration file and applies migrations.
5. `make collectstatic`: Collects static files from `static` and saves them in staticfiles (for reloading static files in production).

To avoid permission problem, on each machine run these commands and restart the docker or os.

`sudo groupadd docker && sudo usermod -aG docker $USER && sudo chown root:docker /var/run/docker.sock`

## Initial Startup
Before you launch WhiskersHub for the first time on your system, copy the `.env.example` file and save it as `.env`, making sure the correct values are placed inside the `.env file`, as `Dockerfile.prod` will copy this file, and WhiskersHub will use to aquire important credentials and values for operation.

*Note:* DO NOT make direct changes to the `.env.example` file unless it is part of your PR.

## Services
For more information about the exact services used in WhiskersHub, refer to the [Technical Architecture Documenbt](https://esbaaroman.sharepoint.com/:u:/s/WhiskersIoT/ETa6X7tpBhBCi7sl_IPenmABmd3lWoVD1HrvD10ybuRFww?e=WC1jvl) on Microsoft Loop.

## External Services
Currently, WhiskersHub is expected to run alongside a network server called [The Things Stack](https://github.com/TheThingsNetwork/lorawan-stack). This is one of many network servers that are to be compatible with WhiskersHub. Its main purpose is to manage LoRaWAN networks, and forward decoded packets to WhiskersHub using a webhook that targets `routes/views.py=>ttn_app`. As of now, this service is not implemented in the `docker-compose.yml` file, and has to be deployed on the side manually, by following this [documentation](https://www.thethingsindustries.com/docs/the-things-stack/host/docker/).

### TTN App Processing
The TTN app endpoint (`/routes/ttn-app/`) is optimized for high-throughput processing of device uplink messages. It uses asynchronous processing to handle multiple requests efficiently:

1. **Asynchronous Processing**: The endpoint processes uplink messages asynchronously:
   - Initial validation happens synchronously
   - Message processing is offloaded to a dedicated thread pool
   - The endpoint returns a 202 Accepted response immediately

2. **Thread Pool**: A dedicated thread pool handles TTN message processing:
   - Configure the pool size with `TTN_POOL_SIZE` in `.env` (default: 10)
   - Adjust based on your server's CPU resources


## Simulating Devices on the Network
In most cases, The Things Stack won't be needed during actual development. Instead, you may simulate its behavior by manually posting JSON packets to the same route while on development mode, based on the templates available in [sharepoint](https://esbaaroman.sharepoint.com/:f:/s/WhiskersIoT/EsH7-QhH2q1OscGoihu_4jMBwh1fqjeyH_gOZWLMGPfQEA?e=Lxndpn). You may use a client such as [Thunder](https://marketplace.visualstudio.com/items?itemName=rangav.vscode-thunder-client) which you can find in the VS Code market place. Make sure you include the Authorization header, according to the `TTN_API_KEY` set in `.env.example` or `.env`.

# Contribution Guidelines
## Formatting
### Formatters
When contributing to the codebase of WhiskersHub, devs are expected to follow the same formatting style used by the repo owner. These include the following:

#### Python — Black
You can read more about this formatter [here](https://pypi.org/project/black/). To install it on your system, use the following command: `pip install black`, and then enable it from VS Code settings.

#### HTML — djLint
This formatter is used to work with Django HTML documents, it is aware of the special django-python syntax, and does not raise an error when working with it. You can read more about this formatter [here](https://www.djlint.com/). To use it, simply install the [djLint VS Code extention](https://marketplace.visualstudio.com/items?itemName=monosans.djlint), then install it in your Python environment using the following command: `pip install -U djlint`.

*Note— 1:* Make sure your djLint instance parses the `.djlintrc` file in the project's root directory. It includes some ignored error codes.

#### JS & CSS — Prettier
To use this formatter, intall the [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) extension from VS Code market place.

### Formatting Guidelines
1. DO NOT format entire documents, only apply formatting to your own changes.
2. If you're on VS Code, go to `Preferences > Editor: Format On Save Mode > modifications`. This applies formatting only to your changes.
3. If you plan on reformatting others work, create a related PR explaining why.

## Logging
During your work on WhiskersHub, you're expected to create helpful logs for the functionalities you implement on the platform. Right now, there are two different log files expected by the workspace.

1. `app.log`: Used to store logs on the user application level, and is writeable by the dev. Don't shy away from logging any events that seem useful in the future. And make sure to use the proper DEBUG, INFO, WARNING, and ERROR levels for the log.
2. `djn.log`: Used to store logs generated by django, which includes HTTP logs, and exceptions that were not caught by the user program. You're not to log to this file directly.

### Print Statements
Print statements are a useful tool for debugging and logging during development. However, it sould always remain as such. You're free to create print statements to assist you in debugging your code, but they're not to be included in the final PR.

Preferably, print statements should be substituded by DEBUG loggs in the `app.log` file.

## Version Control
When a developer notices a bug, has an idea for a feature, or tasked to work on specific requirements, they are expected to follow a specific workflow. This workflow is described below.

### 1. Creating an Issue on Jira
Visit [ESBAAR's Jira Backlog for Whiskers](https://esbaar-tech.atlassian.net/jira/software/projects/WHIS/boards/4/backlog) and create an issue related to the job you're going to perform. When creating an issue, your issue title should follow a Title Case format, and should be assigned an issue type according to the following.

1. `Bug`: Used for software bugs or an unexpected behavior in the user interface or the design of the system, in addition to hotfixes.
2. `Task`: A high-level requirement that describes a feature from the perspective of the end user. It must consist of subtasks that breakdown the technical work needed to get that requirements met.
3. `Story`: Any technical or non technical work that needs to be done, they can be part of a bigger user story or standalone.

*Note:* All developers are encouraged to contribute to the project's backlog, no matter how insignificant or invaluable you think your contribution may be.

### 2. Creating a New Branch
After the issue has been created, you may begin working by creating a new branch from `dev`, which ensures that you're using the latest version of the codebase. The formatting for the branch titles take the following approach:

`WHIS-XXX-Example-of-a-Branch-Title`

Where `XXX` is your issue number, according to the Jira ticketing system.

### 3. Commiting Contributions
When you want to commit a change to the newly created branch, your commits are expected to be short and specific. Here are some commiting guidelines:

1. Try to segment your commits based on unit functions or
2. Do not commit code that prevents the server from starting.
3. Do not include lines or files in your commit that are unrelated to the commit message.
4. Your final commit in the PR should not contain any print statements.

### 4. Creating Pull Requests
Once your confident with the progress you've made on your task, it is time to push your changes to GitHub, and create a pull request. The goal of the pull request is to request feedback from peers and supervisors, minimizing errors and ensuring a standarized codebase. A PR can be created by going to GitHub and issuing a PR for the branch you're working on. Its title should follow the following format:

`[WHIS-XXX] Example of a Pull Request Title`

This ensures that the PR is properly linked to its assigned issue on Jira.

Once the PR has been reviewed, it will either be approved for merge with `dev`, or some changes on your PR will be requested. In this case you have to collaborate with your peer reviewers to reach an understanding on what has to be fixed and apply the required modifications before requesting another review.

### 5. Merging & Squashing
The final process contributing to WhiskersHub is the Merge and Squash, which becomes available as an option once the PR has been approved by all reviewers. This process results in your branch becoming merged with `dev`, before getting archived and the individual commits packed into a single commit message with the merge action.

The final decision of performing the Merge and Squash action should be called by the issue assignee, not the reviewer. This ensures that the assignee is able to reflect on their work and make the final call in merging the branches.

### 6. Automated Docker Workflows
Once your PR is merged with the `dev` branch, a build process is started on GitHub's servers, according to the workflow specified in `.github/workflows/call-docker-build.yaml`.