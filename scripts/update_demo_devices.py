#!/usr/bin/env python
"""
Script to update demo devices every 20 seconds:
- Updates all device statuses
- Moves car and persons in circular patterns
- Sends device status to /routes/ttn-app/ instead of saving device
"""

import os
import sys
import django
import random
import time
import math
import json
import requests
from datetime import datetime

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "app.settings")
django.setup()

from django.utils import timezone
from django.conf import settings
from device_manager.models import Device, get_device_attrs_template
from fields.models import Field

APP_URL='http://web:8000'
# Constants for circular movement (for persons)
CENTER_LAT = 23.567495001727526
CENTER_LNG = 58.32483294611905
PERSON_RADIUS = 0.003  # ~300m
MOVEMENT_SPEED = 0.05  # radians per update (adjust for faster/slower movement)

# Polygon coordinates for car movement
CAR_POLYGON = [
    (23.56397473570651, 58.319948441055274),
    (23.56487989127142, 58.320839601638276),
    (23.568169306536742, 58.32104432771815),
    (23.56898612843209, 58.32304341767458),
    (23.570266541720677, 58.32440424397025),
    (23.574193556479223, 58.32504475703206),
    (23.57395021695685, 58.3257787778131),
    (23.57257605589637, 58.32618483186219),
    (23.571173251647867, 58.3271843495215),
    (23.570758133231685, 58.32799645761968),
    (23.56972748873225, 58.327715343278015),
    (23.56826739517962, 58.32837127674193),
    (23.567022008440507, 58.32862115615675),
    (23.566406468105598, 58.32868362601045),
    (23.565175378782385, 58.3257163079594),
    (23.563801125910018, 58.32253034542037)
]

def generate_iot_payload(device):
    """
    Generate an IoT payload for the device based on the structure in get_iot_payload().
    """
    now = datetime.now()
    timestamp = int(now.timestamp())
    iso_format = now.astimezone().isoformat()

    # Create messages based on device type
    messages = []
    if device.type == "Whiskers Node V1":
        # Update device attributes first
        device.attr["client"]["Air Temperature"] = device.temp
        device.attr["client"]["Battery"] = device.batt  # Make sure battery level is updated in attributes
        device.attr["client"]["Light"] = random.randint(50, 100)
        device.attr["client"]["Frame Counter"] += 1
        device.attr["client"]["RSSI"] = random.randint(-90, -60)
        device.attr["client"]["Latitude"] = device.loca["lati"]
        device.attr["client"]["Longitude"] = device.loca["long"]

        # Create message list for node device
        messages = [
            {
                "measurementId": "3576",
                "measurementValue": {
                    "id": 5,
                    "statusName": "The Bluetooth scan timed out and failed to obtain the location."
                },
                "timestamp": timestamp,
                "type": "Positioning Status"
            },
            {
                "measurementId": "4097",
                "measurementValue": device.temp,
                "timestamp": timestamp*1000,
                "type": "Air Temperature"
            },
            {
                "measurementId": "4199",
                "measurementValue": device.attr["client"]["Light"],
                "timestamp": timestamp*1000,
                "type": "Light"
            },
            {
                "measurementId": "3000",
                "measurementValue": device.batt,  # Use the updated battery level
                "timestamp": timestamp*1000,
                "type": "Battery"
            },
            {
                "measurementId": "4197",
                "measurementValue": device.loca["long"],
                "motionId": 0,
                "timestamp": timestamp*1000,
                "type": "Longitude"
              },
              {
                "measurementId": "4198",
                "measurementValue": device.loca["lati"],
                "motionId": 0,
                "timestamp": timestamp*1000,
                "type": "Latitude"
              },
            {
                "measurementId": "4200",
                "measurementValue": [
                    {
                        "eventName": "Motion event.",
                        "id": 1
                    },
                    {
                        "eventName": "Motionless event.",
                        "id": 2
                    },
                    {
                        "eventName": "Shock event.",
                        "id": 3
                    }
                ],
                "timestamp": timestamp*1000,
                "type": "Event Status"
            }
        ]
    elif device.type == "Whiskers Gateway V1":
        # Update device attributes first
        device.attr["client"]["temp"] = device.temp
        device.attr["client"]["txok"] += random.randint(1, 5)
        device.attr["client"]["rxok"] += random.randint(1, 5)
        device.attr["client"]["rxfw"] += random.randint(1, 5)

        # Create message list for gateway device
        messages = [
            {
                "measurementId": "1001",
                "measurementValue": device.temp,
                "timestamp": timestamp*1000,
                "type": "temp"
            },
            {
                "measurementId": "1002",
                "measurementValue": device.attr["client"]["txok"],
                "timestamp": timestamp*1000,
                "type": "txok"
            },
            {
                "measurementId": "1003",
                "measurementValue": random.randint(0, 10),
                "timestamp": timestamp*1000,
                "type": "txin"
            },
            {
                "measurementId": "1004",
                "measurementValue": random.randint(80, 100),
                "timestamp": timestamp*1000,
                "type": "ackr"
            },
            {
                "measurementId": "4200",
                "measurementValue": [
                    {
                        "eventName": "Motion event.",
                        "id": 1
                    },
                    {
                        "eventName": "Motionless event.",
                        "id": 2
                    },
                    {
                        "eventName": "Shock event.",
                        "id": 3
                    }
                ],
                "timestamp": timestamp*1000,
                "type": "Event Status"
            }
        ]

    # Create the full payload
    iot_payload = {
        "end_device_ids": {
            "device_id": f"eui-{device.euid}",
            "application_ids": {
                "application_id": "whiskershub"
            },
            "dev_eui": device.euid,
            "join_eui": device.euid,
            "dev_addr": "01889B41"
        },
        "correlation_ids": [
            f"gs:uplink:{device.euid}",
            f"rpc:/ttn.lorawan.v3.GsNs/HandleUplink:{device.euid}",
            f"rpc:/ttn.lorawan.v3.NsAs/HandleUplink:{device.euid}"
        ],
        "received_at": iso_format,
        "uplink_message": {
            "session_key_id": "AZDcTrNyj+2ASCoGMDt40g==",
            "f_port": 5,
            "f_cnt": device.attr["client"]["Frame Counter"] if "Frame Counter" in device.attr["client"] else 0,
            "frm_payload": "EQUAAABmuFdsAckAADg=",
            "decoded_payload": {
                "err": 0,
                "messages": [messages],
                "payload": "110500000066b8576c01c9000038",
                "valid": 'true'
            },
            "rx_metadata": [
                {
                    "gateway_ids": {
                        "gateway_id": "eui-1122334455667788",
                        "eui": "1122334455667788"
                    },
                    "time": iso_format,
                    "timestamp": timestamp,
                    "rssi": device.attr["client"].get("RSSI", -76),
                    "channel_rssi": device.attr["client"].get("RSSI", -76),
                    "snr": 14.8,
                    "frequency_offset": "550",
                    "uplink_token": "CiIKIAoUZXVpLTJjZjdmMTEwNDIzMDAwMDESCCz38RBCMAABELDGpc8BGgwIia/htQYQ3oau0wIggOetvdPDQQ==",
                    "channel_index": 3,
                    "received_at": iso_format
                }
            ],
            "settings": {
                "data_rate": {
                    "lora": {
                        "bandwidth": 125000,
                        "spreading_factor": 7,
                        "coding_rate": "4/5"
                    }
                },
                "frequency": "867100000",
                "timestamp": timestamp,
                "time": iso_format
            },
            "received_at": iso_format,
            "consumed_airtime": "0.066816s",
            "version_ids": {
                "brand_id": "sensecap",
                "model_id": "sensecapt1000-tracker-ab",
                "hardware_version": "1.0",
                "firmware_version": "1.0",
                "band_id": "EU_863_870"
            },
            "network_ids": {
                "net_id": "000000"
            }
        }
    }

    return iot_payload

def update_device_status(device):
    """Update general device status and send to TTN endpoint"""
    # Update battery level based on device type
    if device.aset == "Battery":
        # For battery devices, cycle from 100 to 0 and back to 100
        # Check if we need to initialize the battery direction in device attributes
        if "battery_direction" not in device.attr:
            device.attr["battery_direction"] = -1  # Start by decreasing

        # Get the current battery direction from device attributes
        battery_direction = device.attr["battery_direction"]

        # Update battery level based on direction
        if battery_direction == -1:  # Decreasing
            device.batt = max(0, device.batt - 5)  # Decrease by 5% each time
            if device.batt == 0:
                device.attr["battery_direction"] = 1  # Change to increasing
        else:  # Increasing
            device.batt = min(100, device.batt + 5)  # Increase by 5% each time
            if device.batt == 100:
                device.attr["battery_direction"] = -1  # Change to decreasing

        # Make sure the battery level is also updated in client attributes
        device.attr["client"]["Battery"] = device.batt

        print(f"Battery device {device.name} updated: level={device.batt}%, direction={'charging' if device.attr['battery_direction'] == 1 else 'discharging'}")
    elif device.aset != "Solar Panel":
        # For other devices (except Solar Panel), decrease slightly
        device.batt = max(1, device.batt - random.randint(0, 2))
        # Make sure the battery level is also updated in client attributes
        if "client" in device.attr and "Battery" in device.attr["client"]:
            device.attr["client"]["Battery"] = device.batt

    # Update temperature with small random fluctuations
    device.temp = max(10, min(45, device.temp + random.randint(-2, 2)))

    # Update last update timestamp
    device.lupd = timezone.now()


    # Generate IoT payload
    payload = generate_iot_payload(device)

    # Send payload to TTN endpoint
    try:
        # Set headers with API key
        headers = {
            "Authorization": settings.TTN_API_KEY,
            "Content-Type": "application/json"
        }

        # Send the request
        for host in [APP_URL]:
            try:
                response = requests.post(
                    f"{host}/routes/ttn-app/",
                    json=payload,
                    headers=headers
                )
                break;
            except Exception as e:
                print(f"Failed to send update for device {device.name}")
                print(f"Tried {host} !")


        # Check response
        if response.status_code in [200, 202]:
            print(f"{response.text}: Sent update for device: {device.name} (Batt: {device.batt}%, Temp: {device.temp}°C)")
        else:
            print(f"Failed to send update for device {device.name}: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"Error sending update for device {device.name}: {e}")
        # Save the device as fallback
        device.save()
        print(f"Saved device {device.name} as fallback")

def move_in_circle(device, radius, angle_offset, current_time):
    """Move a device in a circular pattern"""
    # Calculate the current angle based on time
    angle = MOVEMENT_SPEED * current_time + angle_offset

    # Calculate new coordinates
    new_lat = CENTER_LAT + radius * math.cos(angle)
    new_lng = CENTER_LNG + radius * math.sin(angle)

    # Update device location
    device.loca["lati"] = new_lat
    device.loca["long"] = new_lng

    # Update device attributes
    if device.type == "Whiskers Node V1":
        device.attr["client"]["Latitude"] = new_lat
        device.attr["client"]["Longitude"] = new_lng

    # Set device as active when moving
    if not device.actv:
        device.actv = True
        device.attr["client"]["Motion event."] = True
        device.attr["client"]["Motionless event."] = False

        # Create movement event
        # Event.objects.create(
        #     devi=device,
        #     type="Info",
        #     desc="is now moving."
        # )
    #update_device_status(device)

    print(f"Moved {device.name} to ({new_lat:.6f}, {new_lng:.6f})")

def move_along_polygon(device, current_time):
    """Move a device along a polygon path"""
    # Calculate the current position index based on time
    # We'll move to a new point every update
    index = current_time % len(CAR_POLYGON)

    # Get the coordinates for the current position
    new_lat, new_lng = CAR_POLYGON[index]

    # Update device location
    device.loca["lati"] = new_lat
    device.loca["long"] = new_lng

    # Update device attributes
    if device.type == "Whiskers Node V1":
        device.attr["client"]["Latitude"] = new_lat
        device.attr["client"]["Longitude"] = new_lng

    # Set device as active when moving
    if not device.actv:
        device.actv = True
        device.attr["client"]["Motion event."] = True
        device.attr["client"]["Motionless event."] = False

        # # Create movement event
        # Event.objects.create(
        #     devi=device,
        #     type="Info",
        #     desc="is now moving."
        # )
    #update_device_status(device)

    print(f"Moved {device.name} to polygon point {index+1}/{len(CAR_POLYGON)}: ({new_lat:.6f}, {new_lng:.6f})")

def get_demo_devices():
    """Get demo devices by logging into the web interface and scraping data"""
    import requests
    from bs4 import BeautifulSoup
    import re

    # Create a session to maintain cookies
    session = requests.Session()

    # Login to the application
    login_url = f"{APP_URL}/accounts/login/"
    login_data = {
        "username": "admin",
        "password": "123asd456",
        "csrfmiddlewaretoken": ""
    }

    # Get CSRF token first
    login_page = session.get(login_url)
    soup = BeautifulSoup(login_page.text, "html.parser")
    csrf_token = soup.find("input", {"name": "csrfmiddlewaretoken"}).get("value")
    login_data["csrfmiddlewaretoken"] = csrf_token

    # Perform login
    session.post(login_url, data=login_data, headers={"Referer": login_url})

    # Get device list page
    device_list_url = f"{APP_URL}/device/list/"
    list_page = session.get(device_list_url)
    soup = BeautifulSoup(list_page.text, "html.parser")

    # Extract device IDs and names
    device_links = soup.select("a[href^='/device/']")
    device_ids = []

    for link in device_links:
        href = link.get("href")
        match = re.search(r'/device/(\d+)/', href)
        if match and "Demo" in link.text:
            device_ids.append(match.group(1))

    # Get detailed info for each device
    devices = []
    for device_id in device_ids:
        edit_url = f"{APP_URL}/device/edit/{device_id}/"
        edit_page = session.get(edit_url)
        soup = BeautifulSoup(edit_page.text, "html.parser")

        # Create device object from scraped data
        device = parse_device_from_edit_page(soup, device_id)
        if device:
            devices.append(device)

    return devices


def parse_device_from_edit_page(soup, device_id):
    """Parse device data from the edit page HTML"""
    try:
        # Extract form field values
        name_input = soup.find("input", {"id": "id_name"})
        desc_textarea = soup.find("textarea", {"id": "id_desc"})
        aset_select = soup.find("select", {"id": "id_aset"})
        type_select = soup.find("select", {"id": "id_type"})
        euid_input = soup.find("input", {"id": "id_euid"})
        fild_select = soup.find("select", {"id": "id_fild"})
        offp_input = soup.find("input", {"id": "id_offp"})
        lati_input = soup.find("input", {"id": "id_lati"})
        long_input = soup.find("input", {"id": "id_long"})
        alti_input = soup.find("input", {"id": "id_alti"})
        plac_select = soup.find("select", {"id": "id_plac"})

        # Extract values with fallbacks
        name = name_input.get("value", "") if name_input else ""
        desc = desc_textarea.text.strip() if desc_textarea else ""
        euid = euid_input.get("value", "") if euid_input else ""
        offp = int(offp_input.get("value", 0)) if offp_input and offp_input.get("value") else 0
        lati = float(lati_input.get("value", 0.0)) if lati_input and lati_input.get("value") else 0.0
        long = float(long_input.get("value", 0.0)) if long_input and long_input.get("value") else 0.0
        alti = float(alti_input.get("value", 0.0)) if alti_input and alti_input.get("value") else 0.0

        # Extract selected values from select elements
        aset = ""
        if aset_select:
            selected_option = aset_select.find("option", {"selected": True})
            aset = selected_option.get("value", "") if selected_option else ""

        device_type = ""
        if type_select:
            selected_option = type_select.find("option", {"selected": True})
            device_type = selected_option.get("value", "") if selected_option else ""

        fild_id = None
        if fild_select:
            selected_option = fild_select.find("option", {"selected": True})
            fild_id = int(selected_option.get("value")) if selected_option and selected_option.get("value") else None

        plac = ""
        if plac_select:
            selected_option = plac_select.find("option", {"selected": True})
            plac = selected_option.get("value", "") if selected_option else ""

        # Get the actual device from database to preserve existing data
        device = Device.objects.get(id=device_id)

        # Update device with scraped data
        device.name = name
        device.desc = desc
        device.euid = euid.upper().replace("  •  ", "") if euid else device.euid
        device.aset = aset if aset else device.aset
        device.type = device_type if device_type else device.type
        device.offp = offp if offp else device.offp

        # Update location data
        device.loca = {
            "lati": lati if lati != 0.0 else device.loca.get("lati", 0.0),
            "long": long if long != 0.0 else device.loca.get("long", 0.0),
            "alti": alti if alti != 0.0 else device.loca.get("alti", 0.0),
            "plac": plac if plac else device.loca.get("plac", "Outdoor"),
            "oofi": device.loca.get("oofi", False)
        }

        # Update field assignment if found
        if fild_id:
            try:
                device.fild = Field.objects.get(id=fild_id)
            except Field.DoesNotExist:
                pass

        # Ensure device has proper attributes for its type
        if device.type and not device.attr:
            device.attr = get_device_attrs_template(device.type)

        print(f"Parsed device: {device.name} (Type: {device.type}, Asset: {device.aset})")
        return device

    except Exception as e:
        print(f"Error parsing device {device_id}: {e}")
        # Fallback to getting device from database
        try:
            return Device.objects.get(id=device_id)
        except Device.DoesNotExist:
            return None

def main():
    """Main function to update devices every 20 seconds"""
    print("Starting device update script...")
    print("Press Ctrl+C to stop")

    try:
        update_count = 0
        # Get all demo devices
        devices = get_demo_devices()
        while True:
            update_count += 1
            current_time = update_count  # Use update count as time
            print(f"\nUpdate #{update_count} at {datetime.now().strftime('%H:%M:%S')}")



            # Process each device
            for device in devices:
                # Generate a random value to decide whether to process this device
                random_value = random.random()

                # Skip this device if random value is under 0.5
                if random_value < 0.5:
                    print(f"Skipping device {device.name} (random value: {random_value:.2f} < 0.5)")
                    continue

                print(f"Processing device {device.name} (random value: {random_value:.2f} >= 0.5)")

                # Move persons in circles
                if device.aset == "Person":
                    # Get person number from name (e.g., "Demo Person 1" -> 1)
                    try:
                        person_num = int(device.name.split()[-1])
                        # Different starting angles for each person
                        angle_offset = (person_num - 1) * (2 * math.pi / 3)
                        move_in_circle(device, PERSON_RADIUS, angle_offset, current_time)
                    except (ValueError, IndexError):
                        pass

                # Move car along the polygon path
                elif device.aset == "Vehicle":
                    move_along_polygon(device, current_time)
                    print(f"Moved {device.name} to polygon point {device.loca['lati']}, {device.loca['long']}")

                # Update general status
                update_device_status(device)
                # Note: We no longer save the device here as we're sending updates to the TTN endpoint

            # Wait for a few seconds before the next update
            wait_time = random.randint(1, 5)
            print(f"Waiting {wait_time} seconds until next update...")
            time.sleep(wait_time)

    except KeyboardInterrupt:
        print("\nScript stopped by user")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
