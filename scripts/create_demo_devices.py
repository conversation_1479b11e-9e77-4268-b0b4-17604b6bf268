#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to create demo devices for WhiskersHub:
- 1 Solar Panel
- 4 Batteries
- 1 Gateway
- 3 Persons
- 1 Car
"""

import os
import sys
import django
import random
import string
import math
from decimal import Decimal

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "app.settings")
django.setup()

from django.utils import timezone
from device_manager.models import Device
from fields.models import Field
from notification_center.models import Event

def generate_eui():
    """Generate a random EUI-64 address (16 hex characters)"""
    return ''.join(random.choices(string.hexdigits, k=16)).upper()

def create_demo_field():
    """Create a demo field if it doesn't exist"""
    # Check if a field already exists
    if Field.objects.exists():
        return Field.objects.first()

    # Create a new field
    field = Field.objects.create(
        name="Demo Field",
        cord=[
            {"lat": 23.573064890405238, "lng": 58.32862310499097},
            {"lat": 23.571204347601522, "lng": 58.31989730078567},
            {"lat": 23.563171255205663, "lng": 58.31953179244246},
            {"lat": 23.56591391282845, "lng": 58.331003994199875}
        ],
        colr="#3688fc",
        covr=Decimal("1.000"),
        loca="Demo Location",
        work_shifts={}
    )

    print(f"Created field: {field.name}")
    return field

def create_device(name, device_type, asset_type, field, latitude, longitude):
    """Create a device with the given parameters"""
    # Check if device with this name already exists
    if Device.objects.filter(name=name).exists():
        print(f"Device '{name}' already exists, skipping creation.")
        return Device.objects.get(name=name)

    # Create device
    device = Device.objects.create(
        name=name,
        desc=f"Demo {asset_type}",
        euid=generate_eui(),
        type=device_type,
        aset=asset_type,
        fild=field,
        offp=60,  # 60 minutes offline period
        stat="Online",
        batt=random.randint(70, 100) if asset_type != "Solar Panel" else 100,
        temp=random.randint(20, 35),
    )

    # Set location
    device.loca = {
        "lati": latitude,
        "long": longitude,
        "alti": 0,
        "oofi": False,
        "plac": "Outdoor"
    }

    # Initialize the attr field with the proper structure
    # This is necessary because the default dict() in the model might not be properly initialized
    if not device.attr:
        device.attr = {}
    if "client" not in device.attr:
        device.attr["client"] = {}
    if "server" not in device.attr:
        device.attr["server"] = {}
    if "shared" not in device.attr:
        device.attr["shared"] = {}

    # Set specific attributes based on device type
    if device_type == "Whiskers Node V1":
        # Set client attributes for Node
        client_attrs = {
            "Air Temperature": device.temp,
            "Battery": device.batt,
            "Light": random.randint(70, 100),
            "Frame Counter": random.randint(1, 100),
            "RSSI": random.randint(-90, -60),
            "Latitude": latitude,
            "Longitude": longitude,
            "Motion event.": False,
            "Motionless event.": False,
            "Shock event.": False
        }
        # Update the client dictionary
        device.attr["client"].update(client_attrs)
    elif device_type == "Whiskers Gateway V1":
        # Set client attributes for Gateway
        client_attrs = {
            "temp": device.temp,
            "txok": random.randint(100, 500),
            "txin": random.randint(0, 10),
            "ackr": random.randint(90, 100),
            "rxok": random.randint(100, 500),
            "rxfw": random.randint(100, 500),
            "IP": "192.168.1." + str(random.randint(2, 254))
        }
        # Update the client dictionary
        device.attr["client"].update(client_attrs)

    device.save()

    # Create an event for the device
    Event.objects.create(
        devi=device,
        type="Info",
        desc="has been created as a demo device."
    )

    print(f"Created device: {device.name} ({device.aset})")
    return device

def main():
    """Create all demo devices"""
    print("Creating demo devices...")

    # Create or get a field
    field = create_demo_field()

    # Center coordinates for our devices
    center_lat = 23.567495001727526
    center_lng = 58.32483294611905

    # Create a solar panel
    solar_panel = create_device(
        name="Demo Solar Panel",
        device_type="Whiskers Node V1",
        asset_type="Solar Panel",
        field=field,
        latitude=center_lat + 0.002,
        longitude=center_lng + 0.002
    )

    # Create 4 batteries
    batteries = []
    for i in range(1, 5):
        battery = create_device(
            name=f"Demo Battery {i}",
            device_type="Whiskers Node V1",
            asset_type="Battery",
            field=field,
            latitude=center_lat + 0.001 * math.cos(i * math.pi/2),
            longitude=center_lng + 0.001 * math.sin(i * math.pi/2)
        )
        batteries.append(battery)

    # Create a gateway
    gateway = create_device(
        name="Demo Gateway",
        device_type="Whiskers Gateway V1",
        asset_type="Station",
        field=field,
        latitude=center_lat,
        longitude=center_lng
    )

    # Create 3 persons
    persons = []
    for i in range(1, 4):
        person = create_device(
            name=f"Demo Person {i}",
            device_type="Whiskers Node V1",
            asset_type="Person",
            field=field,
            latitude=center_lat + 0.003 * math.cos(i * 2*math.pi/3),
            longitude=center_lng + 0.003 * math.sin(i * 2*math.pi/3)
        )
        persons.append(person)

    # Create a car
    car = create_device(
        name="Demo Car",
        device_type="Whiskers Node V1",
        asset_type="Vehicle",
        field=field,
        latitude=center_lat - 0.004,
        longitude=center_lng
    )

    print("Demo devices created successfully!")

if __name__ == "__main__":
    main()
