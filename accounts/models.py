from django.contrib.auth.models import User
from django.db import models
from django.core.validators import RegexValidator
from device_manager.models import Device

from fields.models import Field



# Create your models here.
class UserProfile(models.Model):
    user = models.OneToOneField(
        User, on_delete=models.CASCADE, help_text="User profile owner."
    )
    role = models.CharField(
        max_length=255,
        choices=[
            ("Admin", "Admin"),
            ("Owner", "Owner"),
            ("User", "User"),
        ],
        help_text="User role.",
    )
    phon = models.CharField(max_length=8, null=True, help_text="User phone number.",validators=[
            RegexValidator(
                regex=r'^\d{8}$',  # Regex for international phone numbers
                message="Must be entered in the format: '98765432'. 8 digits allowed."
            )
        ])
    pict = models.ImageField(null=True, upload_to="profile_pictures", blank=True)
    titl = models.CharField(max_length=255, help_text="Title")
    orgn = models.CharField(max_length=255, help_text="Organization")
    dprt = models.CharField(
        blank=True, null=True, max_length=255, help_text="Department"
    )
    devs = models.ManyToManyField(Device, help_text="Devices the user has access to.")

    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name}'s Profile"
