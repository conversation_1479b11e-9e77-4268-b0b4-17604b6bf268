import re
from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from accounts.models import UserProfile

class AccountUtils():
    
    @staticmethod
    def validate_password(form, user=None):
        try:
            validate_password(form.cleaned_data["pwrd"], user)
        except ValidationError as e:
            form.add_error("pwrd", e)
            return False
        return True

    @staticmethod
    def validate_username(form, user=None):
        new_username = form.cleaned_data["usrn"]
        if user is None and User.objects.filter(username=new_username).exists():
            form.add_error("usrn", "Username already in use.")
            return False
            
        if user and user.username != new_username and User.objects.filter(username=new_username).exclude(id=user.id).exists():
            form.add_error("usrn", "Username already in use.")
            return False
        return True

    @staticmethod
    def validate_phone(form, user=None):
        new_phone = form.cleaned_data["phon"]

        if new_phone and not re.match(r'^\d{8}$', new_phone):
            form.add_error("phon", "Phone number must be entered in the format: '98765432'. 8 digits allowed.")
            return False
        
        if user is None and UserProfile.objects.filter(phon=new_phone).exists():
            form.add_error("phon", "Phone number already in use.")
            return False

        if user and new_phone and UserProfile.objects.filter(phon=new_phone).exclude(user__id=user.id).exists():
            form.add_error("phon", "Phone number already in use.")
            return False
        return True

    @staticmethod
    def validate_email(form, user=None):
        new_email = form.cleaned_data["emal"]
        if user is None and User.objects.filter(email=new_email).exists():
            form.add_error("emal", "Email already in use.")
            return False
        
        if user and user.email != new_email and User.objects.filter(email=new_email).exclude(id=user.id).exists():
            form.add_error("emal", "Email already in use.")
            return False
        return True

    @staticmethod
    def validate_fields(form, user=None):
        validate=True
        if not AccountUtils.validate_username(form, user):
            validate=False
        if not AccountUtils.validate_password(form, user):
            validate=False
        if not AccountUtils.validate_phone(form, user):
            validate=False
        if not AccountUtils.validate_email(form, user):
            validate=False
        return validate
