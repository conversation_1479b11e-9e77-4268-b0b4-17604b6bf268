import uuid
import logging
import re

from multiprocessing import Process
from django.conf import settings
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse_lazy
from django.views.generic import TemplateView, FormView, ListView
from accounts.forms import UserAndProfileForm
from notification_center.models import NotificationSettings
from .models import UserProfile
from .utils import AccountUtils
from django.contrib.auth.models import User
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.contrib.auth.decorators import user_passes_test
from django.utils.decorators import method_decorator
from django.contrib.auth import authenticate, login
from django.contrib.auth.mixins import UserPassesTestMixin
from django.core.files.uploadedfile import SimpleUploadedFile
from scripts.communication import *

from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.contrib.auth import logout
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import DetailView
from django.db import transaction
from django.template.response import TemplateResponse

logger = logging.getLogger("app")


# Create your views here.
class AccountLoginView(TemplateView):
    template_name = "accounts/login.html"
    context_object_name = "account_login"

    def get(self, request, *args, **kwargs):
        if not User.objects.filter(is_superuser=True).exists():
            return redirect("accounts:create_admin")
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        username = request.POST.get("username")
        password = request.POST.get("password")
        remember_me = request.POST.get("remember_me")

        user = authenticate(request, username=username, password=password)

        if user:
            login(request, user)
            request.session.set_expiry(1209600 if remember_me else 0)
            return redirect("network_monitor:overview")
        else:
            error_message = "Invalid username or password"
            if username and not User.objects.filter(username=username).exists():
                error_message = "Username does not exist"
            elif username and not password:
                error_message = "Please enter a password"
            return render(request, self.template_name, {"account_login": self.context_object_name, "form_errors": {"login_form": error_message}})


def logout_user(request):
    logout(request)
    return render(request, "accounts/logout.html")


class RecoverPassword(TemplateView):
    template_name = "accounts/recover.html"
    context_object_name = "recover_pswd"


@login_required
def get_user_details(request):
    user = request.user
    user_profile = UserProfile.objects.filter(user_id=user.id).first()
    default_profile_picture = settings.STATIC_URL + "images/profile.png"

    data = {
        "userprofile": {
            "user": {"first_name": user.first_name, "last_name": user.last_name},
            "title": user_profile.titl if user_profile else None,
            "pict": user_profile.pict.url if user_profile and user_profile.pict else default_profile_picture,
        }
    }

    return JsonResponse(data)


@user_passes_test(lambda u: u.is_superuser)
def profile_delete(request, profile_id):
    profile = get_object_or_404(UserProfile, id=profile_id)
    user = get_object_or_404(User, id=profile.user.id)
    user.delete()
    profile.delete()
    return redirect("accounts:list")


@method_decorator(login_required, name="dispatch")
class UserListView(ListView):
    model = UserProfile
    template_name = "accounts/admin/list.html"
    context_object_name = "user_list"

    def get_queryset(self):
        return super().get_queryset().order_by("user__id")


class CreateFirstAdminUserView(FormView):
    template_name = "accounts/admin/setup.html"
    form_class = UserAndProfileForm

    def get(self, request, *args, **kwargs):
        if User.objects.filter(is_superuser=True).exists():
            return redirect("accounts:login")
        return super().get(request, *args, **kwargs)

    def handle_no_permission(self):
        return redirect("network_monitor:overview")

    def get_success_url(self):
        return reverse_lazy("network_monitor:overview")

    def get_form(self, *args, **kwargs):
        form = super().get_form(*args, **kwargs)
        form.fields["role"].disabled = True
        form.is_create = True
        return form

    def form_valid(self, form):
        if not AccountUtils.validate_fields(form):
            return self.form_invalid(form)

        user = self.create_user(form)
        profile = self.create_profile(form, user)
        self.handle_image_upload(form, profile)
        user.save()
        profile.save()
        profile.devs.set(form.cleaned_data["devs"])
        NotificationSettings.objects.create(user=user)
        Process(target=send_invitation, args=(profile, form.cleaned_data["pwrd"])).start()
        return super().form_valid(form)
    
    def form_invalid(self, form):
        context = self.get_context_data(form=form)
        return TemplateResponse(self.request, self.template_name, context, status=409)

    def get_initial(self):
        initial = super().get_initial()
        initial["role"] = "Admin"
        return initial
      
      
    def create_user(self, form):
        return User.objects.create_user(
            username=form.cleaned_data["usrn"],
            password=form.cleaned_data["pwrd"],
            first_name=form.cleaned_data["fnam"],
            last_name=form.cleaned_data["lnam"],
            email=form.cleaned_data["emal"],
            is_superuser=True,
            is_staff=True,
        )

    def create_profile(self, form, user):
        return UserProfile(
            user=user,
            phon=form.cleaned_data["phon"],
            titl=form.cleaned_data["titl"],
            orgn=form.cleaned_data["orgn"],
        )

    def handle_image_upload(self, form, profile):
        if "pict" in self.request.FILES:
            uploaded_file = self.request.FILES["pict"]
            image_extension = "." + uploaded_file.name.split(".")[-1]
            profile.pict = SimpleUploadedFile(str(uuid.uuid1()) + image_extension, uploaded_file.read())


class UserProfileUpdateView(UserPassesTestMixin, FormView):
    template_name = "accounts/admin/create.html"
    form_class = UserAndProfileForm    

    def test_func(self):
        user = self.request.user
        profile_id = self.kwargs.get("profile_id")
        self.profile = get_object_or_404(UserProfile, id=profile_id)
        if profile_id:
            # Check if user is superuser or if user ID matches the profile's user ID
            return user.is_superuser or user.id == self.profile.user.id
        return user.is_superuser

    def handle_no_permission(self):
        return redirect("accounts:list")

    def get_success_url(self):
        return reverse_lazy("accounts:list") if self.request.user.is_superuser else reverse_lazy("network_monitor:overview")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["is_create"] = self.kwargs.get("profile_id") is None
        kwargs["user"] = self.profile.user  # Pass user for device filtering
        return kwargs

    def get_form(self, *args, **kwargs):
        form = super().get_form(*args, **kwargs)
        if not self.request.user.is_superuser:
            for field in ["usrn", "role", "devs"]:
                form.fields[field].disabled = True
        return form

    def form_valid(self, form):
        is_create = self.kwargs.get("profile_id") is None
        profile_id = self.kwargs.get("profile_id")
        username = form.cleaned_data["usrn"]

        user = self.get_or_create_user(username, profile_id)
        if not AccountUtils.validate_fields(form,user):
            return self.form_invalid(form)

        self.update_user_and_profile(form, user, profile_id, is_create)
        return super().form_valid(form)

    def form_invalid(self, form):
        context = self.get_context_data(form=form)
        return TemplateResponse(self.request, self.template_name, context, status=409)


    def get_or_create_user(self, username, profile_id):
        try:
            # Get the profile first
            profile = get_object_or_404(UserProfile, id=profile_id)

            # Check if the username exists but belongs to a different user
            existing_user = User.objects.filter(username=username).first()
            if existing_user and existing_user.id != profile.user.id:
                return None

            # Get the user associated with this profile
            user = profile.user
            # Update username if it has changed
            if user.username != username:
                user.username = username

        except UserProfile.DoesNotExist:
            # This should not happen as we already checked in test_func
            return None

        return user

    @transaction.atomic
    def update_user_and_profile(self, form, user, profile_id, is_create):
        user.first_name = form.cleaned_data["fnam"]
        user.last_name = form.cleaned_data["lnam"]
        user.email = form.cleaned_data["emal"]
        user.is_superuser = form.cleaned_data["role"] == "Admin"
        user.is_staff = user.is_superuser

        profile = get_object_or_404(UserProfile, id=profile_id)
        profile.user = user
        profile.role = form.cleaned_data["role"]
        profile.phon = form.cleaned_data["phon"]
        profile.titl = form.cleaned_data["titl"]
        profile.orgn = form.cleaned_data["orgn"]

        self.handle_image_upload(form, profile)
        self.set_user_password(form, user, is_create)
        self.save_user_and_profile(user, profile, form.cleaned_data["devs"])

    def handle_image_upload(self, form, profile):
        if "pict" in self.request.FILES:
            uploaded_file = self.request.FILES["pict"]
            image_extension = "." + uploaded_file.name.split(".")[-1]
            profile.pict = SimpleUploadedFile(str(uuid.uuid1()) + image_extension, uploaded_file.read())

    def set_user_password(self, form, user, is_create):
        new_password = form.cleaned_data["pwrd"]
        if self.request.user.is_superuser and (not user.has_usable_password() or not user.check_password(new_password)):
            user.set_password(new_password)
            if not is_create:
                Process(target=send_pass_change, args=(user.userprofile, new_password)).start()
        elif not self.request.user.is_superuser and not user.check_password(new_password):
            form.add_error("pwrd", "Incorrect, please try again. If you forgot your password, contact an administrator.")

    @transaction.atomic
    def save_user_and_profile(self, user, profile, devices):
        user.save()
        profile.full_clean()
        profile.save()
        profile.devs.set(devices)

    def get_initial(self):
        initial = super().get_initial()
        profile_id = self.kwargs.get("profile_id")
        if profile_id:
            profile = get_object_or_404(UserProfile, id=profile_id)
            initial.update({
                "usrn": profile.user.username,
                "fnam": profile.user.first_name,
                "lnam": profile.user.last_name,
                "emal": profile.user.email,
                "role": profile.role,
                "titl": profile.titl,
                "orgn": profile.orgn,
                "phon": profile.phon,
                "pict": profile.pict,
                "devs": profile.devs.all(),
            })
        return initial


@method_decorator(user_passes_test(lambda u: u.is_superuser), name="dispatch")
class UserProfileCreateView(UserPassesTestMixin, FormView):
    template_name = "accounts/admin/create.html"
    form_class = UserAndProfileForm

    def test_func(self):
        user = self.request.user
        profile_id = self.kwargs.get("profile_id")
        if profile_id:
            profile = get_object_or_404(UserProfile, id=profile_id)
            # Check if user is superuser or if user ID matches the profile's user ID
            return user.is_superuser or user.id == profile.user.id
        return user.is_superuser

    def handle_no_permission(self):
        return redirect("accounts:list")

    def get_success_url(self):
        return reverse_lazy("accounts:list") if self.request.user.is_superuser else reverse_lazy("network_monitor:overview")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["is_create"] = self.kwargs.get("profile_id") is None
        kwargs["user"] = self.request.user  # Pass user for device filtering
        return kwargs

    def get_form(self, *args, **kwargs):
        form = super().get_form(*args, **kwargs)
        if not self.request.user.is_superuser:
            for field in ["usrn", "role", "devs"]:
                form.fields[field].disabled = True
        return form

    def form_valid(self, form):
        if not AccountUtils.validate_fields(form):
            return self.form_invalid(form)

        user = self.create_user(form)
        profile = self.create_profile(form, user)
        self.handle_image_upload(form, profile)
        user.save()
        profile.save()
        profile.devs.set(form.cleaned_data["devs"])
        NotificationSettings.objects.create(user=user)
        Process(target=send_invitation, args=(profile, form.cleaned_data["pwrd"])).start()
        return super().form_valid(form)
    
    def form_invalid(self, form):
        context = self.get_context_data(form=form)
        return TemplateResponse(self.request, self.template_name, context, status=409)

    def create_user(self, form):
        return User.objects.create_user(
            username=form.cleaned_data["usrn"],
            password=form.cleaned_data["pwrd"],
            first_name=form.cleaned_data["fnam"],
            last_name=form.cleaned_data["lnam"],
            email=form.cleaned_data["emal"],
            is_superuser=form.cleaned_data["role"] == "Admin",
            is_staff=form.cleaned_data["role"] == "Admin",
        )

    def create_profile(self, form, user):
        return UserProfile(
            user=user,
            role=form.cleaned_data["role"],
            phon=form.cleaned_data["phon"],
            titl=form.cleaned_data["titl"],
            orgn=form.cleaned_data["orgn"],
        )

    def handle_image_upload(self, form, profile):
        if "pict" in self.request.FILES:
            uploaded_file = self.request.FILES["pict"]
            image_extension = "." + uploaded_file.name.split(".")[-1]
            profile.pict = SimpleUploadedFile(str(uuid.uuid1()) + image_extension, uploaded_file.read())


@method_decorator(login_required, name="dispatch")
class UserProfileViewer(LoginRequiredMixin, DetailView):
    model = UserProfile
    pk_url_kwarg = "profile_id"
    context_object_name = "profile"

    def get_template_names(self):
        self.template_name = "accounts/view.html"
        return [self.template_name]