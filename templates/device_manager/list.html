{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
    Devices
{% endblock title %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row mt-1">
            <div class="col-lg">
                <div class="page-title-box">
                    <h4 class="page-title">Devices</h4>
                </div>
            </div>
            <div class="col-lg-auto">
                <div class="modal-footer d-flex justify-content-center align-items-center px-0 mt-1">
                    <a href="{% url 'device_manager:create' %}"
                       class="btn btn-success rounded-pill mb-3 {% if not user.is_superuser %}disabled{% endif %}"><i class="mdi mdi-plus me-1"></i>Create Device</a>
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- end row-->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table id="devicesTable"
                           class="table table-centered table-nowrap order-column mb-0 hover">
                        <thead class="table-light">
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Asset</th>
                                <th>Field</th>
                                <th>Status</th>
                                <th>Activity</th>
                                <th>Battery</th>
                                <th>Temperature</th>
                                <th>Last Update</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for device in list %}
                                <tr>
                                    <td>
                                        <a href="{% url 'device_manager:detail' device.id %}" class="text-title"><b>{{ device.name }}</b></a>
                                    </td>
                                    <td>{{ device.type }}</td>
                                    <td>{{ device.aset }}</td>
                                    <td>{{ device.fild }}</td>
                                    <td>
                                        <i class="mdi mdi-circle" id="status-icon-{{ device.id }}"></i> {{ device.stat }}
                                    </td>
                                    {% if device.actv == False %}
                                        <td class="text-secondary">Stationary</td>
                                    {% else %}
                                        <td class="text-danger">Moving</td>
                                    {% endif %}
                                    <td>
                                        <div class="row align-items-center">
                                            <div class="col-md-3">
                                                {% if device.chrg %}
                                                    <i class="mdi mdi-power-plug mdi-18px"></i>
                                                {% else %}
                                                    <i class="mdi mdi-power-plug-off mdi-18px"></i>
                                                {% endif %}
                                            </div>
                                            <div class="col-md-9">
                                                <div class="progress progress-sm" style="background-color: #c5d3e2">
                                                    <i class="progress-bar progress-lg"
                                                       id="bg-color-{{ device.id }}"
                                                       style="width: {{ device.batt }}%"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    {% if device.temp < 60 %}
                                        <td>{{ device.temp }}°C</td>
                                    {% elif device.temp == None %}
                                        <td class="text-danger">{{ device.temp }}°C</td>
                                    {% else %}
                                        <td>—</td>
                                    {% endif %}

                                    {% if device.lupd != None %}
                                        <td>{{ device.lupd }}</td>
                                    {% else %}
                                        <td>Never</td>
                                    {% endif %}
                                    <!-- Action Buttons -->
                                    <td class="table-action">
                                        {% if user.is_superuser %}
                                            <a href="{% url 'device_manager:edit' device_id=device.id %}"
                                               class="action-icon"> <i class="mdi mdi-pencil"></i></a>
                                            <a class="action-icon"><i class="mdi mdi-wifi-cog" style="opacity: 0.5;"></i></a>
                                            <a class="action-icon"><i class="mdi mdi-cog" style="opacity: 0.5;"></i></a>
                                            <a href="#"
                                               data-toggle="modal"
                                               data-target="#deleteModal-{{ device.id }}"
                                               class="action-icon"><i class="mdi mdi-delete"></i></a>
                                        {% else %}
                                            <a class="action-icon" disabled> <i class="mdi mdi-pencil" style="opacity: 0.5;"></i></a>
                                            <a class="action-icon"><i class="mdi mdi-wifi-cog" style="opacity: 0.5;"></i></a>
                                            <a class="action-icon"><i class="mdi mdi-cog" style="opacity: 0.5;"></i></a>
                                            <a class="action-icon"><i class="mdi mdi-delete" style="opacity: 0.5;"></i></a>
                                        {% endif %}
                                    </td>
                                    <!-- The Modal -->
                                    <div class="modal fade"
                                         id="deleteModal-{{ device.id }}"
                                         tabindex="-1"
                                         role="dialog"
                                         aria-labelledby="deleteModalLabel-{{ device.id }}"
                                         aria-hidden="true">
                                        <div class="modal-dialog modal-dialog-centered" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h4 class="modal-title" id="deleteModalLabel-{{ device.id }}">Delete Confirmation</h4>
                                                </div>
                                                <div class="modal-body">
                                                    Are you sure you want to delete <b>{{ device.name }}</b>? This action cannot be undone.
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                    <a href="{% url 'device_manager:delete' device.id %}" method="POST">
                                                        <button type="button" class="btn btn-danger">Delete</button>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </tr>

                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <!-- end row-->
            </div>
        </div>
    </div>
    <!-- container -->
{% endblock content %}
{% block extra_javascript %}
    <!-- Datatables -->
    <script src="{% static 'js/vendor.min.js' %}"></script>
    <script src="{% static 'js/vendor/datatables.min.js' %}"></script>
    <script src="{% static 'js/custom/data_tables.js' %}"></script>
    <!-- Third party js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.6.1/font/bootstrap-icons.css"
          rel="stylesheet" />
    <!-- Third party js ends -->

    <!-- Device status and battery indicators -->
    <script src="{% static 'js/custom/device_indicators.js' %}"></script>
    <!-- Init js -->
    <!-- Init js end -->
{% endblock extra_javascript %}
