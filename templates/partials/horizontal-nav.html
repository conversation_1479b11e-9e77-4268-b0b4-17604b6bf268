{% load static %}
<!-- Topbar Start -->
<div class="navbar-custom topnav-navbar topnav-navbar-dark">
    <div class="container-fluid">
        <!-- LOGO -->
        <a href="{% url 'network_monitor:overview' %}" class="topnav-logo">
            <span class="topnav-logo-lg">
                <img src="{% static 'images/whiskers_logo_gray.svg' %}" alt="" height="30" class="filter-white">
            </span>
            <span class="topnav-logo-sm">
                <img src="{% static 'images/whiskers_logo.svg' %}" alt="" height="30">
            </span>
        </a>
        <script>
            function toggleDropdown(dropdownId) {
                var dropdown = document.getElementById(dropdownId);
                var button = dropdown.previousElementSibling;
            
                dropdown.classList.toggle('show');
                button.classList.toggle('show');
              }
        </script>
        <ul class="list-unstyled topbar-menu float-end mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link arrow-none"
                   href="#"
                   role="button"
                   onclick="toggleDropdown('notification-dropdown')">
                    <i class="dripicons-bell noti-icon"></i>
                    <span class="noti-icon-badge" id="notification-badge"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-menu-animated dropdown-lg"
                     id="notification-dropdown"
                     aria-labelledby="topbar-notifydrop">
                    <div class="dropdown-item noti-title border-top-0 border-start-0 border-end-0 border-dashed border">
                        <h5 class="m-0">
                            <span class="float-end">
                                {% csrf_token %}
                                <a href="javascript: void(0);"
                                   id="clear-notifications-btn"
                                   class="text-dark">
                                    <small>Clear All</small>
                                </a>
                            </span>Notifications
                        </h5>
                    </div>
                    <div class="px-3" style="max-height: 300px;" data-simplebar>
                        <div id="notification-container"></div>
                        <div class="text-muted text-center my-3"
                             id="no-notification-message"
                             style="display:none">No recent notifications.</div>
                    </div>
                    <!-- All-->
                    <a href="{% url 'notification_center:list' %}"
                       class="dropdown-item text-center text-primary notify-item notify-all border-top">View All</a>
                </div>
            </li>
            <li class="notification-list">
                {% if user.is_superuser %}
                    <a class="nav-link end-bar-toggle" href="/settings">
                        <i class="dripicons-gear noti-icon"></i>
                    </a>
                {% else %}
                    <a class="nav-link end-bar-toggle">
                        <i class="dripicons-gear noti-icon" style="opacity:0.5;"></i>
                    </a>
                {% endif %}
            </li>
            {% if user.userprofile %}
            <li class="dropdown notification-list">
                <a class="nav-link nav-user arrow-none me-0"
                   href="#"
                   role="button"
                   onclick="toggleDropdown('profile-dropdown')">
                    <span class="account-user-avatar">
                        <img id="profile-picture"
                             src="{% static 'images/profile.png' %}"
                             alt="user-image"
                             class="rounded-circle">
                    </span>
                    <span>
                        <span id="user-name" class="account-user-name"></span>
                        <span id="user-position" class="account-position"></span>
                    </span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-menu-animated topbar-dropdown-menu profile-dropdown"
                     id="profile-dropdown"
                     aria-labelledby="topbar-userdrop">
                    <!-- item-->
                    <div class=" dropdown-header noti-title">
                        <h6 class="text-overflow m-0">Welcome!</h6>
                    </div>
                    <!-- item-->
                        <a href="{% url 'accounts:edit' profile_id=user.userprofile.id %}"
                           class="dropdown-item notify-item">
                            <i class="mdi mdi-account-circle me-1"></i>
                            <span>My Account</span>
                        </a>
                    <!-- item-->
                    <a href="#"
                       data-bs-toggle="modal"
                       data-bs-target="#staticBackdrop"
                       class="dropdown-item notify-item">
                        <i class="mdi mdi-logout me-1"></i>
                        <span>Logout</span>
                        <form method="post" id="logout-form" action="#">
                            {% csrf_token %}
                            {% if redirect_field_value %}
                                <input type="hidden"
                                       name="{{ redirect_field_name }}"
                                       value="{{ redirect_field_value }}" />
                            {% endif %}
                        </form>
                    </a>
                </div>
            </li>
            {% endif %}
        </ul>
        <a class="navbar-toggle"
           data-bs-toggle="collapse"
           data-bs-target="#topnav-menu-content">
            <div class="lines">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </a>
    </div>
</div>
{% include "./logout_modal.html" %}
<!-- end Topbar -->
<div class="topnav shadow-sm">
    <div class="container-fluid">
        <nav class="navbar navbar-light navbar-expand-lg topnav-menu">
            <div class="collapse navbar-collapse" id="topnav-menu-content">
                <ul class="navbar-nav">
                    <a class="nav-link arrow-none"
                       href="{% url 'network_monitor:overview' %}"
                       id="topnav-dashboards"
                       role="button"
                       aria-haspopup="true"
                       aria-expanded="false">
                        <i class="uil-dashboard me-1"></i>Network Monitor
                    </a>
                    <li class="dropdown">
                        <a class="nav-link dropdown-toggle arrow-none"
                           id="topnav-apps"
                           role="button"
                           style="pointer-events: none"
                           aria-haspopup="true"
                           aria-expanded="false">
                            <i class="uil-box me-1"></i>Packet Analyzer
                            <div class="arrow-down"></div>
                        </a>
                        <div class="dropdown-menu" aria-labelledby="topnav-dashboards">
                            <a href="{% url 'packet_analyzer:uplink_list' %}" class="dropdown-item"><i class="uil-upload me-1"></i>Uplink</a>
                            <a href="{% url 'packet_analyzer:downlink_list' %}"
                               class="dropdown-item"><i class="uil-down-arrow me-1"></i>Downlink</a>
                            {% if user.is_superuser %}
                                <a href="{% url 'packet_analyzer:dropped_list' %}" class="dropdown-item"><i class="uil-ban me-1"></i>Dropped</a>
                            {% endif %}
                        </div>
                    </li>
                    <a class="nav-link arrow-none"
                       href="{% url 'device_manager:list' %}"
                       id="topnav-apps"
                       role="button"
                       aria-haspopup="true"
                       aria-expanded="false">
                        <i class="uil-mobile-vibrate me-1"></i>Devices
                    </a>
                    <a class="nav-link arrow-none select"
                       href="{% url 'fields:list' %}"
                       id="topnav-apps"
                       role="button"
                       aria-haspopup="true"
                       aria-expanded="false">
                        <i class="uil-map-pin me-1"></i>Fields
                    </a>
                    <a class="nav-link arrow-none"
                       href="{% url 'notification_center:list' %}"
                       id="topnav-apps"
                       role="button"
                       aria-haspopup="true"
                       aria-expanded="false">
                        <i class="uil-bell me-1"></i>Notifications
                    </a>
                    <a class="nav-link arrow-none"
                       href="{% url 'accounts:list' %}"
                       id="topnav-apps"
                       role="button"
                       aria-haspopup="true"
                       aria-expanded="false">
                        <i class="uil-users-alt me-1"></i>Users
                    </a>
                </ul>
            </div>
        </nav>
    </div>
</div>
{% block extra_javascript %}
    <script src="{% static 'js/custom/websockets.js' %}"></script>
    <script src="{% static 'js/custom/notifications_list.js' %}"></script>
    <script src="{% static 'js/custom/user_details.js' %}"></script>
{% endblock extra_javascript %}
