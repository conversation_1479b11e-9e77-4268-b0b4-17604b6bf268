{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
    Packet Analyzer
{% endblock title %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <h4 class="page-title">
                        <a href="#" onclick="javascript:history.back()" class="link-secondary"><i class="dripicons-arrow-thin-left"></i></a> Telemetry Viewer
                    </h4>
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- end row-->
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3 class="mb-0">{{ title }}</h3>
                    <div class="date-filter-controls">
                        <div class="row g-2 align-items-center">
                            <div class="col-auto">
                                <label for="start-date" class="form-label mb-0">From:</label>
                            </div>
                            <div class="col-auto">
                                <input type="datetime-local" id="start-date" class="form-control form-control-sm">
                            </div>
                            <div class="col-auto">
                                <label for="end-date" class="form-label mb-0">To:</label>
                            </div>
                            <div class="col-auto">
                                <input type="datetime-local" id="end-date" class="form-control form-control-sm">
                            </div>
                            <div class="col-auto">
                                <button id="apply-filter" class="btn btn-primary btn-sm">Apply</button>
                            </div>
                            <div class="col-auto">
                                <button id="reset-filter" class="btn btn-secondary btn-sm">Last 7 Days</button>
                            </div>
                        </div>
                    </div>
                </div>
                <script>
                    var key = "{{ key }}";
                    var device_id = "{{ device_id }}";
                </script>
                <div id="telemetry" class="apex-charts"></div>
                <div id="timeline" class="apex-charts"></div>
            </div>
        </div>
    </div>
    <!-- container -->
{% endblock content %}
{% block extra_javascript %}
    <!-- Third party js -->
    <script src="{% static 'js/vendor/apexcharts.min.js' %}"></script>
    <script src="{% static 'js/custom/websockets.js' %}"></script>
    <script src="{% static 'js/custom/telemetry.js' %}"></script>
    <!-- Third party js ends -->
    <!-- Init js -->
    <!-- Init js end -->
{% endblock extra_javascript %}
